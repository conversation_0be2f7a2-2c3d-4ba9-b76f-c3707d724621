================================================================================
硬编码检测报告
================================================================================

总计发现 334 个硬编码问题：
  高危险: 40 个
  中危险: 250 个
  低危险: 44 个

HIGH 级别问题:
----------------------------------------

文件: backend/agents/session_context.py
位置: 第301行, 第16列
类型: 数据库更新硬编码
内容: 
                UPDATE conversations
                SET domain_id = ?, category_id = ?, updated_at...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/session_context.py
位置: 第386行, 第16列
类型: 数据库查询硬编码
内容: 
                SELECT document_id FROM documents
                WHERE conversation_id = ? AND use...
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/template_version_manager.py
位置: 第247行, 第20列
类型: 数据库查询硬编码
内容: 
                SELECT * FROM template_versions 
                WHERE template_id = ? AND version ...
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/template_version_manager.py
位置: 第283行, 第29列
类型: 数据库更新硬编码
内容: 
                UPDATE template_versions 
                SET status = ?, updated_at = ?
          ...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/template_version_manager.py
位置: 第294行, 第29列
类型: 数据库更新硬编码
内容: 
                UPDATE template_versions 
                SET status = ?, updated_at = ?
          ...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/template_version_manager.py
位置: 第418行, 第16列
类型: 数据库查询硬编码
内容: 
            SELECT version FROM template_versions
            WHERE template_id = ?
            ORD...
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/template_version_manager.py
位置: 第441行, 第16列
类型: 数据库查询硬编码
内容: 
            SELECT version FROM template_versions
            WHERE template_id = ? AND language = ...
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/template_version_manager.py
位置: 第452行, 第16列
类型: 数据库插入硬编码
内容: 
            INSERT INTO template_versions (
                template_id, version, content, variable...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/unified_state_manager.py
位置: 第128行, 第16列
类型: 数据库查询硬编码
内容: 
                SELECT document_id FROM documents
                WHERE conversation_id = ? AND use...
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/unified_state_manager.py
位置: 第226行, 第16列
类型: 数据库查询硬编码
内容: 
                SELECT status FROM focus_points_status 
                WHERE session_id = ? AND us...
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/unified_state_manager.py
位置: 第273行, 第16列
类型: 数据库查询硬编码
内容: 
                SELECT point_id, status FROM focus_points_status 
                WHERE session_id ...
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/unified_state_manager.py
位置: 第296行, 第16列
类型: 数据库更新硬编码
内容: 
                UPDATE focus_points_status 
                SET status = 'pending', updated_at = ?
...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/unified_state_manager.py
位置: 第369行, 第20列
类型: 数据库删除硬编码
内容: 
                    DELETE FROM focus_points_status 
                    WHERE session_id = ? AND u...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/dynamic_reply_generator.py
位置: 第1378行, 第20列
类型: 数据库查询硬编码
内容: SELECT name FROM domains WHERE domain_id = ?
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/dynamic_reply_generator.py
位置: 第1391行, 第20列
类型: 数据库查询硬编码
内容: SELECT name FROM categories WHERE category_id = ?
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1207行, 第16列
类型: 数据库更新硬编码
内容: 
                UPDATE conversations
                SET status = ?,
                    metadata =...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1398行, 第16列
类型: 数据库更新硬编码
内容: 
                UPDATE conversations
                SET status = 'active',
                    met...
建议: 考虑使用配置文件管理此硬编码

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1455行, 第16列
类型: 数据库更新硬编码
内容: 
                UPDATE conversations
                SET metadata = ?,
                    updated_...
建议: 考虑使用配置文件管理此硬编码

文件: backend/utils/test_helper.py
位置: 第82行, 第16列
类型: 数据库查询硬编码
内容: SELECT conversation_id FROM conversations WHERE conversation_id = ? AND user_id = ?
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/utils/test_helper.py
位置: 第89行, 第20列
类型: 数据库更新硬编码
内容: UPDATE conversations 
                       SET domain_id = ?, category_id = ?, updated_at = ?
    ...
建议: 考虑使用配置文件管理此硬编码

文件: backend/utils/test_helper.py
位置: 第98行, 第20列
类型: 数据库插入硬编码
内容: INSERT INTO conversations 
                       (conversation_id, user_id, domain_id, category_id,...
建议: 考虑使用配置文件管理此硬编码

文件: backend/utils/test_helper.py
位置: 第115行, 第16列
类型: 数据库删除硬编码
内容: DELETE FROM domain_recognition_results WHERE conversation_id = ?
建议: 考虑使用配置文件管理此硬编码

文件: backend/utils/test_helper.py
位置: 第121行, 第16列
类型: 数据库插入硬编码
内容: INSERT INTO domain_recognition_results 
                   (conversation_id, domain_id, confidence, ...
建议: 考虑使用配置文件管理此硬编码

文件: backend/utils/test_helper.py
位置: 第138行, 第16列
类型: 数据库删除硬编码
内容: DELETE FROM category_recognition_results WHERE conversation_id = ?
建议: 考虑使用配置文件管理此硬编码

文件: backend/utils/test_helper.py
位置: 第144行, 第16列
类型: 数据库插入硬编码
内容: INSERT INTO category_recognition_results 
                   (conversation_id, category_id, confiden...
建议: 考虑使用配置文件管理此硬编码

文件: backend/utils/test_helper.py
位置: 第189行, 第16列
类型: 数据库更新硬编码
内容: UPDATE conversations 
                   SET domain_id = NULL, category_id = NULL, status = 'active'...
建议: 考虑使用配置文件管理此硬编码

文件: backend/scripts/init_domain_data.py
位置: 第94行, 第27列
类型: 数据库查询硬编码
内容: SELECT COUNT(*) FROM domains
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/scripts/init_domain_data.py
位置: 第97行, 第27列
类型: 数据库查询硬编码
内容: SELECT COUNT(*) FROM categories
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/scripts/init_domain_data.py
位置: 第106行, 第27列
类型: 数据库查询硬编码
内容: SELECT domain_id, name, description FROM domains ORDER BY name
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/data/db/admin_manager.py
位置: 第84行, 第16列
类型: 数据库查询硬编码
内容: SELECT COUNT(*) FROM admin_users WHERE role = ?
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/data/db/admin_manager.py
位置: 第94行, 第29列
类型: 数据库插入硬编码
内容: 
                    INSERT INTO admin_users (username, password_hash, salt, role, email)
          ...
建议: 考虑使用配置文件管理此硬编码

文件: backend/data/db/admin_manager.py
位置: 第135行, 第16列
类型: 数据库更新硬编码
内容: UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
建议: 考虑使用配置文件管理此硬编码

文件: backend/data/db/admin_manager.py
位置: 第198行, 第40列
类型: 数据库查询硬编码
内容: 
                SELECT COUNT(DISTINCT user_id) FROM conversations 
                WHERE 
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/data/db/admin_manager.py
位置: 第223行, 第20列
类型: 数据库查询硬编码
内容: SELECT COUNT(*) FROM documents WHERE user_id = ?
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/data/db/admin_manager.py
位置: 第245行, 第16列
类型: 数据库查询硬编码
内容: SELECT COUNT(*) FROM conversations WHERE user_id = ?
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/data/db/admin_manager.py
位置: 第264行, 第20列
类型: 数据库查询硬编码
内容: SELECT COUNT(*) FROM messages WHERE conversation_id = ?
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/data/db/admin_manager.py
位置: 第345行, 第29列
类型: 数据库插入硬编码
内容: 
                    INSERT INTO admin_logs (admin_id, action, resource, details, ip_address)
      ...
建议: 考虑使用配置文件管理此硬编码

文件: backend/data/db/admin_manager.py
位置: 第373行, 第40列
类型: 数据库查询硬编码
内容: 
                SELECT COUNT(*) FROM admin_logs al 
                WHERE 
建议: 使用 get_unified_config().get_database_query() 或 self._get_query()

文件: backend/handlers/conversation_handler.py
位置: 第394行, 第16列
类型: 数据库更新硬编码
内容: 
                UPDATE conversations
                SET domain_id = NULL, category_id = NULL, upda...
建议: 考虑使用配置文件管理此硬编码

文件: backend/handlers/conversation_handler.py
位置: 第447行, 第16列
类型: 数据库更新硬编码
内容: 
                UPDATE conversations
                SET status = 'completed', updated_at = ?
     ...
建议: 考虑使用配置文件管理此硬编码

MEDIUM 级别问题:
----------------------------------------

文件: backend/config/unified_dynamic_config.py
位置: 第633行, 第28列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/config/unified_dynamic_config.py
位置: 第634行, 第43列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/config/service.py
位置: 第86行, 第31列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第109行, 第53列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第143行, 第67列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/config/service.py
位置: 第150行, 第31列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第156行, 第31列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/config/service.py
位置: 第189行, 第31列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第457行, 第65列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第466行, 第67列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第475行, 第69列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第483行, 第31列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/service.py
位置: 第512行, 第124列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/config/knowledge_base_config.py
位置: 第55行, 第25列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/config/knowledge_base_config.py
位置: 第56行, 第40列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/knowledge_base_config.py
位置: 第62行, 第38列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/config/knowledge_base_config.py
位置: 第64行, 第35列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/config/knowledge_base_config.py
位置: 第70行, 第42列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/config/settings.py
位置: 第35行, 第19列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/config/settings.py
位置: 第59行, 第37列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/config/settings.py
位置: 第166行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/unified_config_loader.py
位置: 第127行, 第50列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/unified_config_loader.py
位置: 第269行, 第67列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/config/unified_config_loader.py
位置: 第276行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/tests/config/test_knowledge_base_config.py
位置: 第79行, 第29列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/tests/config/test_knowledge_base_config.py
位置: 第80行, 第44列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/tests/config/test_knowledge_base_config.py
位置: 第203行, 第52列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/tests/config/test_settings.py
位置: 第27行, 第52列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/tests/api/test_main.py
位置: 第28行, 第51列
类型: 问候消息硬编码
内容: 正在处理您的请求。
您好！很高兴为您服务。
建议: 使用 get_unified_config().get_message_template()

文件: backend/tests/api/test_main.py
位置: 第30行, 第66列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/tests/api/test_main.py
位置: 第31行, 第70列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/tests/api/test_main.py
位置: 第81行, 第26列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/tests/api/test_main.py
位置: 第85行, 第26列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/tests/api/test_main.py
位置: 第126行, 第31列
类型: 问候消息硬编码
内容: 正在处理您的请求。
您好！很高兴为您服务。
建议: 使用 get_unified_config().get_message_template()

文件: backend/tests/api/test_main.py
位置: 第148行, 第31列
类型: 问候消息硬编码
内容: 正在处理您的请求。
您好！很高兴为您服务。
建议: 使用 get_unified_config().get_message_template()

文件: backend/tests/api/test_main.py
位置: 第291行, 第66列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/tests/api/test_main.py
位置: 第292行, 第70列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/tests/api/test_main.py
位置: 第348行, 第66列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/tests/api/test_main.py
位置: 第349行, 第67列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/tests/api/test_main.py
位置: 第507行, 第43列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/session_context.py
位置: 第68行, 第32列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/session_context.py
位置: 第116行, 第66列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/unified_llm_client_factory.py
位置: 第23行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/unified_llm_client_factory.py
位置: 第110行, 第31列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/unified_llm_client_factory.py
位置: 第119行, 第31列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/unified_llm_client_factory.py
位置: 第169行, 第74列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/knowledge_base.py
位置: 第87行, 第45列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/knowledge_base.py
位置: 第120行, 第73列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/knowledge_base.py
位置: 第361行, 第41列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/context_analyzer.py
位置: 第319行, 第38列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/context_analyzer.py
位置: 第325行, 第38列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/context_analyzer.py
位置: 第338行, 第38列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/context_analyzer.py
位置: 第339行, 第30列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/keyword_accelerator.py
位置: 第220行, 第46列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/keyword_accelerator.py
位置: 第342行, 第24列
类型: 问候消息硬编码
内容: 您好！我是您的AI助手，很高兴为您服务。请告诉我您的需求，我会帮助您进行需求分析和文档生成。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/decision_monitor.py
位置: 第228行, 第76列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow_message_mixin.py
位置: 第148行, 第8列
类型: 问候消息硬编码
内容: 
        获取问候消息

        用于欢迎用户并询问如何提供帮助的标准问候语。

        返回:
            str: 问候消息
        
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/llm_service.py
位置: 第45行, 第32列
类型: URL硬编码
内容: https://autogen.com
建议: 使用配置文件或环境变量

文件: backend/agents/llm_service.py
位置: 第87行, 第61列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_service.py
位置: 第260行, 第59列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_service.py
位置: 第270行, 第34列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/llm_service.py
位置: 第348行, 第56列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/llm_service.py
位置: 第361行, 第60列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/llm_service.py
位置: 第380行, 第48列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/llm_service.py
位置: 第638行, 第106列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_utils.py
位置: 第161行, 第48列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/message_reply_manager.py
位置: 第109行, 第34列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/message_reply_manager.py
位置: 第161行, 第39列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/message_reply_manager.py
位置: 第167行, 第39列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/message_reply_manager.py
位置: 第266行, 第64列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/review_and_refine.py
位置: 第112行, 第59列
类型: 错误消息硬编码
内容: 抱歉，我没有找到需要修改的文档。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/review_and_refine.py
位置: 第123行, 第59列
类型: 错误消息硬编码
内容: 抱歉，处理您的修改请求时出错。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/review_and_refine.py
位置: 第147行, 第59列
类型: 错误消息硬编码
内容: 抱歉，保存修改后的文档时出错，请稍后再试。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/review_and_refine.py
位置: 第162行, 第55列
类型: 错误消息硬编码
内容: 抱歉，处理您的修改请求时发生了内部错误。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/review_and_refine.py
位置: 第325行, 第30列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/review_and_refine.py
位置: 第325行, 第33列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/simplified_decision_engine.py
位置: 第359行, 第32列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/simplified_decision_engine.py
位置: 第532行, 第29列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/simplified_decision_engine.py
位置: 第543行, 第66列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/simplified_decision_engine.py
位置: 第701行, 第67列
类型: 问候消息硬编码
内容: 你好，请介绍自己
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/simplified_decision_engine.py
位置: 第1173行, 第19列
类型: 错误消息硬编码
内容: 抱歉，系统遇到了一些问题，请稍后再试。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/simplified_decision_engine.py
位置: 第1192行, 第13列
类型: 问候消息硬编码
内容: 你好，我想设计一张海报
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/simplified_decision_engine.py
位置: 第1194行, 第13列
类型: 问候消息硬编码
内容: 你好，请问如何注册账号？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_state_machine.py
位置: 第80行, 第52列
类型: 问候消息硬编码
内容: 您好！我是您的AI需求分析师。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_state_machine.py
位置: 第81行, 第54列
类型: 问候消息硬编码
内容: 您好！
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_state_machine.py
位置: 第249行, 第25列
类型: 问候消息硬编码
内容: 您好！有什么新的项目需要我帮您分析吗？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第65行, 第56列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第66行, 第86列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第72行, 第79列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第149行, 第27列
类型: 错误消息硬编码
内容: 抱歉，我在知识库中没有找到相关信息。您可以尝试换个问法，或者告诉我您的具体需求，我可以帮您进行需求分析。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第207行, 第23列
类型: 错误消息硬编码
内容: 抱歉，处理您的问题时出现了错误，请稍后再试
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第299行, 第23列
类型: 错误消息硬编码
内容: 抱歉，我无法基于现有信息生成合适的回答。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第387行, 第19列
类型: 错误消息硬编码
内容: 抱歉，我没有找到相关信息。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/domain_classifier.py
位置: 第152行, 第42列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/domain_classifier.py
位置: 第312行, 第73列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategy_registry.py
位置: 第246行, 第46列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/base.py
位置: 第60行, 第23列
类型: 错误消息硬编码
内容: 正在处理您的请求。
抱歉，
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第52行, 第25列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/dynamic_reply_generator.py
位置: 第124行, 第12列
类型: 错误消息硬编码
内容: 抱歉，我无法
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第353行, 第43列
类型: 错误消息硬编码
内容: 抱歉，暂时无法生成个性化回复。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第387行, 第47列
类型: 错误消息硬编码
内容: 抱歉，我需要重新组织一下语言。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第399行, 第43列
类型: 错误消息硬编码
内容: 抱歉，暂时无法生成回复。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第404行, 第39列
类型: 错误消息硬编码
内容: 抱歉，生成回复时出现错误。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第647行, 第41列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/dynamic_reply_generator.py
位置: 第897行, 第59列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/dynamic_reply_generator.py
位置: 第927行, 第29列
类型: 问候消息硬编码
内容: 您好！很高兴为您服务。请问有什么可以帮您？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第999行, 第29列
类型: 错误消息硬编码
内容: 抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第1022行, 第29列
类型: 错误消息硬编码
内容: 非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第1204行, 第29列
类型: 错误消息硬编码
内容: 抱歉，我暂时无法生成合适的回复。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/dynamic_reply_generator.py
位置: 第1347行, 第101列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/dynamic_reply_generator.py
位置: 第1475行, 第42列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/llm_config_manager.py
位置: 第149行, 第39列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_config_manager.py
位置: 第158行, 第35列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_config_manager.py
位置: 第167行, 第39列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_config_manager.py
位置: 第177行, 第31列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_config_manager.py
位置: 第188行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/llm_config_manager.py
位置: 第191行, 第27列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/llm_config_manager.py
位置: 第222行, 第41列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/decision_types.py
位置: 第27行, 第11列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/conversation_flow/state_manager.py
位置: 第101行, 第92列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow/utils.py
位置: 第16行, 第26列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/conversation_flow/utils.py
位置: 第17行, 第29列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/conversation_flow/utils.py
位置: 第18行, 第27列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow/message_processor.py
位置: 第80行, 第25列
类型: 错误消息硬编码
内容: 抱歉，处理消息时遇到了问题。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/message_processor.py
位置: 第336行, 第68列
类型: 错误消息硬编码
内容: 抱歉，无法处理您的请求。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第468行, 第25列
类型: 错误消息硬编码
内容: 抱歉，我遇到了一些技术问题。请稍后再试。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第498行, 第91列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第501行, 第87列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第553行, 第8列
类型: 问候消息硬编码
内容: 
        根据配置键获取关键词列表
        
        例如: "simple_commands.greeting_keywords" -> ["你好", "hi", ...]
...
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第614行, 第29列
类型: 错误消息硬编码
内容: 抱歉，无法处理其他会话的消息。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第621行, 第25列
类型: 错误消息硬编码
内容: 抱歉，处理消息时遇到了问题。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第763行, 第19列
类型: 错误消息硬编码
内容: 很抱歉，我在处理您的请求时遇到了问题。我是AI需求采集助手，可以帮助您整理业务需求。请告诉我您需要什么帮助？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第772行, 第19列
类型: 问候消息硬编码
内容: 您好！我是AI需求采集助手，可以帮助您整理和分析业务需求。请告诉我您想要咨询什么内容？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第776行, 第19列
类型: 错误消息硬编码
内容: 抱歉，我暂时无法处理您的请求。请稍后再试。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1101行, 第36列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1202行, 第32列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1267行, 第71列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1450行, 第67列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1509行, 第34列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1571行, 第30列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1908行, 第19列
类型: 错误消息硬编码
内容: 抱歉，我在处理您的回答时遇到了问题。请重新描述一下您的需求。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1915行, 第26列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1945行, 第28列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第2201行, 第60列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第2203行, 第34列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第2432行, 第19列
类型: 错误消息硬编码
内容: 抱歉，我可能没有完全理解您的意思。

您刚才提到："
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第2447行, 第19列
类型: 错误消息硬编码
内容: 抱歉我没理解清楚，请再详细说明一下您的项目需求，我会认真倾听。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第2551行, 第19列
类型: 错误消息硬编码
内容: 抱歉，我可能没有完全理解您的意思。作为需求采集助手，我主要专长于帮助整理业务需求、分析功能规格和生成需求文档。您能重新描述一下您的需要吗？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第2555行, 第19列
类型: 错误消息硬编码
内容: 抱歉，请您换个方式描述，我会尽力帮助您。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/capabilities_strategy.py
位置: 第171行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/capabilities_strategy.py
位置: 第202行, 第42列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/capabilities_strategy.py
位置: 第208行, 第38列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/capabilities_strategy.py
位置: 第214行, 第38列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/capabilities_strategy.py
位置: 第272行, 第85列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategies/capabilities_strategy.py
位置: 第330行, 第23列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/capabilities_strategy.py
位置: 第334行, 第19列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第124行, 第15列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第220行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第243行, 第38列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第249行, 第38列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第262行, 第38列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第264行, 第38列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第401行, 第19列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第436行, 第28列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第221行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第244行, 第38列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第254行, 第38列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第261行, 第38列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第270行, 第38列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第331行, 第27列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第388行, 第19列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第388行, 第57列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第413行, 第19列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第1行, 第0列
类型: 问候消息硬编码
内容: 
问候策略实现

处理用户的问候消息，包括：
- 基本问候（你好、hi、hello等）
- 时间相关问候（早上好、下午好等）
- 礼貌性问候回应

优先级: 9 (最高)
支持意图: greeting...
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第29行, 第12列
类型: 问候消息硬编码
内容: 您好！我是AI助手，很高兴为您服务。有什么可以帮助您的吗？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第30行, 第12列
类型: 问候消息硬编码
内容: Hi！欢迎使用我们的服务，我可以帮您解答问题或协助处理需求。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第31行, 第12列
类型: 问候消息硬编码
内容: 您好！我是您的专属AI助手，随时为您提供帮助。请告诉我您需要什么？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第33行, 第12列
类型: 问候消息硬编码
内容: 您好！欢迎来到这里，我是AI助手，有任何问题都可以问我。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第139行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第142行, 第34列
类型: 问候消息硬编码
内容: 您好！有什么可以帮助您的吗？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第162行, 第38列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第170行, 第42列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第176行, 第29列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第177行, 第38列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第185行, 第38列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第204行, 第16列
类型: 问候消息硬编码
内容: 您好！看起来您心情不错，有什么开心的事情需要我帮忙处理吗？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第210行, 第16列
类型: 问候消息硬编码
内容: 您好，我注意到您可能遇到了一些困扰，我会尽力帮助您解决问题。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/greeting_strategy.py
位置: 第249行, 第23列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第175行, 第27列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第198行, 第38列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第209行, 第31列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第217行, 第38列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第225行, 第38列
类型: 阈值硬编码
内容: 0.7
建议: 使用 get_unified_config().get_threshold("key", 0.7)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第333行, 第19列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第333行, 第57列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第350行, 第31列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第387行, 第19列
类型: 阈值硬编码
内容: 0.9
建议: 使用 get_unified_config().get_threshold("key", 0.9)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第387行, 第57列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategies/requirement_strategy.py
位置: 第404行, 第31列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/agents/strategies/fallback_strategy.py
位置: 第31行, 第16列
类型: 错误消息硬编码
内容: 抱歉，我没有完全理解您的意思。能否请您再详细说明一下？
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/fallback_strategy.py
位置: 第72行, 第12列
类型: 错误消息硬编码
内容: 抱歉，刚才的处理出现了异常。请您重新描述一下需求。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/fallback_strategy.py
位置: 第170行, 第34列
类型: 错误消息硬编码
内容: 抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。
建议: 使用 get_unified_config().get_message_template()

文件: backend/agents/strategies/fallback_strategy.py
位置: 第204行, 第30列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/utils/common_imports.py
位置: 第249行, 第40列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/utils/logging_config.py
位置: 第722行, 第26列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/utils/performance_init.py
位置: 第172行, 第36列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/utils/performance_init.py
位置: 第176行, 第48列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/utils/performance_init.py
位置: 第186行, 第41列
类型: 超时时间硬编码
内容: 5.0
建议: 使用 get_unified_config().get_threshold("timeout", 5.0)

文件: backend/utils/safety_manager.py
位置: 第101行, 第42列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/utils/safety_manager.py
位置: 第112行, 第32列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/utils/safety_manager.py
位置: 第358行, 第79列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/utils/intent_manager.py
位置: 第240行, 第20列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/utils/performance_middleware.py
位置: 第1行, 第0列
类型: 澄清请求硬编码
内容: 
性能监控中间件

此模块提供FastAPI性能监控中间件，用于自动跟踪API请求的性能指标。
主要功能包括：
1. 自动记录API响应时间
2. 跟踪请求成功率和错误率
3. 监控并发请求数量
4....
建议: 使用 get_unified_config().get_message_template()

文件: backend/models/admin.py
位置: 第38行, 第42列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/api/main.py
位置: 第125行, 第63列
类型: URL硬编码
内容: https://api.deepseek.com
建议: 使用配置文件或环境变量

文件: backend/api/main.py
位置: 第139行, 第32列
类型: URL硬编码
内容: https://api.deepseek.com
建议: 使用配置文件或环境变量

文件: backend/api/main.py
位置: 第454行, 第24列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/api/main.py
位置: 第468行, 第24列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/api/main.py
位置: 第511行, 第32列
类型: 错误消息硬编码
内容: 抱歉，处理您的请求超时，请稍后再试。
建议: 使用 get_unified_config().get_message_template()

文件: backend/api/main.py
位置: 第525行, 第32列
类型: 错误消息硬编码
内容: 抱歉，系统未能生成有效响应，请稍后再试。
建议: 使用 get_unified_config().get_message_template()

文件: backend/api/main.py
位置: 第536行, 第59列
类型: 错误消息硬编码
内容: 抱歉，系统处理失败，请稍后再试。
建议: 使用 get_unified_config().get_message_template()

文件: backend/api/main.py
位置: 第601行, 第26列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/api/main.py
位置: 第602行, 第24列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/api/main.py
位置: 第955行, 第19列
类型: 错误消息硬编码
内容: 抱歉，查询知识库时出现错误
建议: 使用 get_unified_config().get_message_template()

文件: backend/data/db/admin_manager.py
位置: 第130行, 第55列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/data/db/admin_manager.py
位置: 第144行, 第26列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/data/db/admin_manager.py
位置: 第167行, 第26列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/data/db/admin_manager.py
位置: 第169行, 第65列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/data/db/admin_manager.py
位置: 第169行, 第54列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/data/db/admin_manager.py
位置: 第233行, 第43列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/data/db/admin_manager.py
位置: 第273行, 第58列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/data/db/admin_manager.py
位置: 第276行, 第31列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/data/db/admin_manager.py
位置: 第310行, 第33列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/data/db/admin_manager.py
位置: 第312行, 第58列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/data/db/admin_manager.py
位置: 第397行, 第31列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/data/db/admin_manager.py
位置: 第399行, 第32列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/data/db/message_manager.py
位置: 第187行, 第89列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

文件: backend/handlers/document_handler.py
位置: 第96行, 第25列
类型: 错误消息硬编码
内容: 抱歉，文档修改失败。请重新描述您的修改需求。
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/conversation_handler.py
位置: 第348行, 第28列
类型: 问候消息硬编码
内容: 您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。请告诉我您想要做什么项目？
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/conversation_handler.py
位置: 第353行, 第26列
类型: 问候消息硬编码
内容: 您好！我是AI需求分析师，很高兴为您服务！请告诉我您的项目需求。
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/base_action_handler.py
位置: 第181行, 第12列
类型: 错误消息硬编码
内容: 抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/knowledge_base_handler.py
位置: 第156行, 第16列
类型: 错误消息硬编码
内容: 抱歉，我在知识库中没有找到相关信息。
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/general_request_handler.py
位置: 第161行, 第15列
类型: 澄清请求硬编码
内容: 我理解您的请求，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/general_request_handler.py
位置: 第178行, 第25列
类型: 问候消息硬编码
内容: 您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/general_request_handler.py
位置: 第438行, 第25列
类型: 澄清请求硬编码
内容: 我理解您的问题，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/general_request_handler.py
位置: 第550行, 第25列
类型: 问候消息硬编码
内容: 您好！我是由己AI助手，专门为您提供业务领域的专业建议和指导。

🏢 **由己平台核心业务**：
• 智能需求采集系统 - 通过AI对话收集和分析项目需求
• 在线用工平台 - 连接企业与全球优秀人才...
建议: 使用 get_unified_config().get_message_template()

文件: backend/handlers/general_request_handler.py
位置: 第774行, 第34列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/handlers/general_request_handler.py
位置: 第782行, 第34列
类型: 阈值硬编码
内容: 0.8
建议: 使用 get_unified_config().get_threshold("key", 0.8)

文件: backend/handlers/general_request_handler.py
位置: 第1021行, 第32列
类型: 重试次数硬编码
内容: 3
建议: 使用 get_unified_config().get_business_rule("retry.max_attempts", 3)

文件: backend/services/conversation_history_service.py
位置: 第94行, 第21列
类型: 超时时间硬编码
内容: 5
建议: 使用 get_unified_config().get_threshold("timeout", 5)

LOW 级别问题:
----------------------------------------

文件: backend/config/unified_dynamic_config.py
位置: 第193行, 第70列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/config/unified_dynamic_config.py
位置: 第448行, 第53列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/config/unified_dynamic_config.py
位置: 第491行, 第42列
类型: 文件路径硬编码
内容: backend/config/business_rules.yaml
建议: 使用配置文件或相对路径

文件: backend/config/unified_dynamic_config.py
位置: 第630行, 第26列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/config/knowledge_base_config.py
位置: 第63行, 第35列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/config/knowledge_base_config.py
位置: 第71行, 第39列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/config/settings.py
位置: 第34行, 第15列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/config/unified_config_loader.py
位置: 第21行, 第19列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/config/unified_config_loader.py
位置: 第129行, 第42列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/decision_engine_adapter.py
位置: 第169行, 第61列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/decision_monitor.py
位置: 第275行, 第45列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/unified_decision_engine.py
位置: 第99行, 第45列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/message_reply_manager.py
位置: 第108行, 第27列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/knowledge_base_manager.py
位置: 第163行, 第37列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/knowledge_base_manager.py
位置: 第394行, 第29列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/rag_knowledge_base_agent.py
位置: 第71行, 第73列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/domain_classifier.py
位置: 第51行, 第61列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/decision_cache.py
位置: 第244行, 第57列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/dynamic_reply_generator.py
位置: 第54行, 第19列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/dynamic_reply_generator.py
位置: 第119行, 第27列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/decision_types.py
位置: 第167行, 第50列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/conversation_flow/message_processor.py
位置: 第130行, 第31列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第380行, 第26列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第495行, 第58列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/conversation_flow/core_refactored.py
位置: 第1800行, 第57列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/strategies/emotional_support_strategy.py
位置: 第401行, 第57列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/strategies/knowledge_base_strategy.py
位置: 第271行, 第30列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/agents/strategies/greeting_strategy.py
位置: 第92行, 第44列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/logging_config.py
位置: 第102行, 第17列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/logging_config.py
位置: 第302行, 第58列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/performance_init.py
位置: 第164行, 第40列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/performance_init.py
位置: 第170行, 第37列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/performance_init.py
位置: 第188行, 第45列
类型: 限制数量硬编码
内容: 10.0
建议: 使用 get_unified_config().get_business_rule("limit", 10.0)

文件: backend/utils/performance_init.py
位置: 第195行, 第47列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/performance_init.py
位置: 第217行, 第77列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/intent_manager.py
位置: 第31行, 第8列
类型: 文件路径硬编码
内容: 
        初始化意图管理器
        
        Args:
            config_path: 配置文件路径，默认为 backend/config/intent_d...
建议: 使用配置文件或相对路径

文件: backend/utils/performance_monitor.py
位置: 第230行, 第28列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/progress_indicator.py
位置: 第162行, 第25列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/utils/prompt_loader.py
位置: 第28行, 第35列
类型: 文件路径硬编码
内容: logs/prompt.log
建议: 使用配置文件或相对路径

文件: backend/api/main.py
位置: 第66行, 第18列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/api/main.py
位置: 第357行, 第16列
类型: 文件路径硬编码
内容: /openapi.json
建议: 使用配置文件或相对路径

文件: backend/api/main.py
位置: 第685行, 第26列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/data/db/admin_manager.py
位置: 第240行, 第48列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)

文件: backend/services/conversation_history_service.py
位置: 第117行, 第21列
类型: 限制数量硬编码
内容: 10
建议: 使用 get_unified_config().get_business_rule("limit", 10)