# 需求采集项目文档中心

## 📚 文档导航

### 🏗️ 核心架构文档
- [核心架构](./core-architecture/) - 系统核心架构设计文档
  - [统一决策引擎架构设计](./core-architecture/统一决策引擎架构设计.md) - 系统核心决策引擎的架构设计
  - [统一决策引擎实施计划](./core-architecture/统一决策引擎实施计划.md) - 决策引擎的实施路线图
  - [统一决策引擎文档索引](./core-architecture/统一决策引擎文档索引.md) - 决策引擎相关文档索引
  - [混合AI代理系统总结](./core-architecture/hybrid-ai-agent-summary.md) - 混合AI代理系统的整体架构

### 🎯 意图管理系统
- [意图管理](./intent-management/) - 意图管理系统相关文档
  - [意图管理快速参考](./intent-management/意图管理快速参考.md) - 意图管理系统的快速使用指南
  - [意图管理统一化维护指南](./intent-management/意图管理统一化维护指南.md) - 意图管理系统的维护指南
  - [意图管理统一化实施跟踪](./intent-management/意图管理统一化实施跟踪.md) - 意图管理统一化的实施进度
  - [CHANGELOG-意图管理统一化](./intent-management/CHANGELOG-意图管理统一化.md) - 意图管理统一化的详细变更记录

### 🖥️ 管理后台系统
- [管理后台系统](./admin-system/) - 管理后台系统相关文档
  - [管理后台系统总体规划](./admin-system/admin-system-master-plan.md) - 管理后台的整体规划
  - [管理后台需求文档](./admin-system/admin-dashboard-requirements.md) - 管理后台的功能需求
  - [管理后台API设计](./admin-system/admin-api-design.md) - 管理后台的API接口设计
  - [管理前端架构](./admin-system/admin-frontend-architecture.md) - 管理前端的技术架构

### 🛠️ 开发文档
- [开发指南](./development/) - 开发相关的指南和工具文档
  - [配置管理](./development/configuration/) - 系统配置相关文档
  - [开发指南](./development/guides/) - 代码规范和开发指南
  - [开发工具](./development/tools/) - 开发工具使用说明
  - [统一决策引擎API文档](./development/统一决策引擎API文档.md) - API接口文档

### 📁 历史文档
- [历史文档归档](./archive/) - 历史版本的设计文档和迁移记录

## 🚀 快速开始

### 新开发者入门
1. 阅读 [混合AI代理系统总结](./core-architecture/hybrid-ai-agent-summary.md) 了解系统整体架构
2. 查看 [统一决策引擎架构设计](./core-architecture/统一决策引擎架构设计.md) 理解核心决策逻辑
3. 参考 [意图管理快速参考](./intent-management/意图管理快速参考.md) 了解意图管理系统
4. 查阅 [开发指南](./development/guides/) 了解开发规范

### 系统维护
1. 参考 [意图管理统一化维护指南](./intent-management/意图管理统一化维护指南.md) 进行意图管理
2. 查看 [管理后台系统总体规划](./admin-system/admin-system-master-plan.md) 了解后台管理功能
3. 使用 [开发工具](./development/tools/) 进行系统监控和维护

### 配置管理
1. 查看 [配置管理文档](./development/configuration/) 了解系统配置
2. 参考 [统一决策引擎API文档](./development/统一决策引擎API文档.md) 进行API配置

## 📖 文档维护

### 文档更新原则
- 核心架构变更时，及时更新相关设计文档
- 新功能开发完成后，补充相应的使用指南
- 重要变更记录在CHANGELOG中
- 过时文档及时归档到archive目录

### 文档结构说明
- **根目录**: 核心文档和快速参考
- **development/**: 开发相关的技术文档
- **archive/**: 历史文档和过时设计的归档

## 🔍 文档搜索

### 按功能查找
- **决策引擎**: 搜索"决策引擎"相关文档
- **意图管理**: 查看意图管理相关文档
- **配置系统**: 查看development/configuration目录
- **API接口**: 查看admin-api-design.md和API文档

### 按类型查找
- **架构设计**: 查看架构设计相关文档
- **使用指南**: 查看快速参考和维护指南
- **开发规范**: 查看development/guides目录
- **历史记录**: 查看archive目录和CHANGELOG

---

**最后更新**: 2025年7月30日  
**维护者**: 项目开发团队
