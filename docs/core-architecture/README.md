# 核心架构文档

本目录包含系统核心架构的设计文档和技术规范。

## 📋 文档列表

### 🏗️ 系统架构
- **[混合AI代理系统总结](./hybrid-ai-agent-summary.md)** - 系统整体架构概览
  - 系统组件介绍
  - 架构设计原则
  - 技术栈说明

### ⚙️ 决策引擎
- **[统一决策引擎架构设计](./统一决策引擎架构设计.md)** - 决策引擎的详细架构设计
  - 决策流程设计
  - 组件交互关系
  - 扩展性考虑

- **[统一决策引擎实施计划](./统一决策引擎实施计划.md)** - 决策引擎的实施路线图
  - 实施阶段划分
  - 里程碑定义
  - 风险评估

- **[统一决策引擎文档索引](./统一决策引擎文档索引.md)** - 决策引擎相关文档的索引
  - 文档分类
  - 快速查找指南

## 🎯 阅读建议

### 新开发者
1. 先阅读 **混合AI代理系统总结** 了解整体架构
2. 再查看 **统一决策引擎架构设计** 理解核心逻辑
3. 参考 **统一决策引擎文档索引** 查找具体技术文档

### 架构师
1. 重点关注 **统一决策引擎架构设计** 的技术细节
2. 参考 **统一决策引擎实施计划** 了解演进路径
3. 使用 **文档索引** 快速定位相关资料

### 项目经理
1. 查看 **统一决策引擎实施计划** 了解项目进度
2. 参考 **混合AI代理系统总结** 了解系统能力
3. 关注实施计划中的风险点和里程碑

## 🔄 文档维护

- **更新频率**: 架构变更时及时更新
- **维护责任**: 架构师和技术负责人
- **版本控制**: 重要变更需要版本记录

---

**最后更新**: 2025年7月30日
