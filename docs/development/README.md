# 开发文档目录

本目录包含项目的技术文档和开发指南。

## 📚 文档列表

### 🔧 关键词配置重构 (2025-08-08) ⭐ 最新
- [关键词配置重构文档](./关键词配置重构文档.md) - 完整的重构技术文档
- [关键词配置重构-快速参考](./关键词配置重构-快速参考.md) - 快速参考指南
- [ADR-关键词配置重构技术决策](./ADR-关键词配置重构技术决策.md) - 技术决策记录
- [关键词配置迁移指南](./关键词配置迁移指南.md) - 迁移操作指南

### 系统架构
- [系统架构文档](./系统架构文档.md) - 整体系统架构设计
- [数据库设计文档](./数据库设计文档.md) - 数据库结构和设计说明
- [API接口文档](./API接口文档.md) - REST API接口规范

### 开发指南
- [开发环境搭建](./开发环境搭建.md) - 本地开发环境配置
- [代码规范](./代码规范.md) - 编码标准和最佳实践
- [测试指南](./测试指南.md) - 单元测试和集成测试

### 部署运维
- [部署指南](./部署指南.md) - 生产环境部署流程
- [监控告警](./监控告警.md) - 系统监控和告警配置
- [故障排查](./故障排查.md) - 常见问题和解决方案

## 🎯 关键词配置重构概述

### 重构成果
- ✅ 解决了8个位置的重复关键词定义问题
- ✅ 建立了统一关键词配置系统
- ✅ 修复了意图识别错误
- ✅ 提升了系统性能（缓存优化）
- ✅ 通过了100%的集成测试

### 核心文件
```
backend/config/
├── keywords_config.yaml      # 统一关键词配置
└── keywords_loader.py        # 统一关键词加载器
```

### 快速开始
```python
from backend.config.keywords_loader import get_keywords_loader

# 获取关键词加载器
loader = get_keywords_loader()

# 获取所有知识库关键词
keywords = loader.get_all_knowledge_base_keywords_flat()
print(f"加载了 {len(keywords)} 个关键词")
```

## 📖 文档使用指南

### 新开发者
1. 先阅读 [关键词配置重构-快速参考](./关键词配置重构-快速参考.md)
2. 了解 [系统架构文档](./系统架构文档.md)
3. 按照 [开发环境搭建](./开发环境搭建.md) 配置环境

### 维护人员
1. 查看 [关键词配置重构文档](./关键词配置重构文档.md) 了解详细技术细节
2. 参考 [关键词配置迁移指南](./关键词配置迁移指南.md) 进行配置维护
3. 遵循 [代码规范](./代码规范.md) 进行开发

### 决策者
1. 阅读 [ADR-关键词配置重构技术决策](./ADR-关键词配置重构技术决策.md) 了解技术决策
2. 查看重构成果和性能提升数据
3. 了解后续优化计划

## 🔄 文档更新记录

### 2025-08-08
- ✅ 新增关键词配置重构相关文档
- ✅ 完成技术决策记录
- ✅ 创建迁移指南和快速参考

### 历史记录
- 其他文档的更新记录...

---

**文档维护**: 开发团队  
**最后更新**: 2025-08-08
