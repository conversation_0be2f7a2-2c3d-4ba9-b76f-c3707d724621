# 统一决策引擎 API 文档

## 📖 概述

本文档描述了统一决策引擎的API接口，包括核心决策接口、性能监控接口和管理接口。

## 🎯 核心决策接口

### 1. 直接使用统一决策引擎

```python
from backend.agents.unified_decision_engine import get_unified_decision_engine
from backend.agents.decision_types import create_decision_context
from backend.agents.unified_state_manager import ConversationState

# 获取引擎实例
engine = get_unified_decision_engine()

# 创建决策上下文
context = create_decision_context(
    session_id='user_session_123',
    user_id='user_456',
    message='用户消息',
    current_state=ConversationState.IDLE,
    conversation_history=[],
    user_profile={},
    business_context={}
)

# 执行决策
result = await engine.make_decision(context)
```

**返回结果结构**：
```python
DecisionResult(
    action='send_greeting',           # 决策动作
    confidence=0.95,                  # 置信度 (0.0-1.0)
    intent='greeting',                # 识别的意图
    emotion='positive',               # 情感分析结果
    response_template='您好！...',     # 回复模板
    next_state=ConversationState.IDLE, # 下一个状态
    strategy_name='greeting_strategy', # 使用的策略
    execution_time=0.001,             # 执行时间（秒）
    metadata={}                       # 额外元数据
)
```

### 2. 使用兼容适配器（推荐）

```python
from backend.agents.decision_engine_adapter import get_decision_engine_adapter

# 获取适配器实例
adapter = get_decision_engine_adapter(use_unified_engine=True)

# 使用旧格式上下文（与现有代码兼容）
context = [{
    'session_id': 'user_session_123',
    'user_id': 'user_456',
    'current_state': 'IDLE',
    'conversation_history': [],
    'user_profile': {},
    'business_context': {}
}]

# 执行决策（与旧接口完全兼容）
result = await adapter.analyze('用户消息', context)
```

**返回结果结构**：
```python
{
    "decision": {
        "action": "send_greeting",
        "priority": 9,
        "response_template": "您好！...",
        "next_state": "IDLE"
    },
    "intent": "greeting",
    "emotion": "positive",
    "confidence": 0.95,
    "metadata": {
        "strategy_name": "greeting_strategy",
        "execution_time": 0.001,
        "unified_engine_result": True
    }
}
```

## 📊 性能监控接口

### 1. 获取性能统计

```python
# 获取完整性能统计
stats = engine.get_performance_stats()

# 返回结构
{
    "overall": {
        "total_requests": 1000,
        "successful_requests": 950,
        "failed_requests": 50,
        "success_rate_percent": 95.0,
        "error_rate_percent": 5.0,
        "average_response_time_ms": 1.5,
        "min_response_time_ms": 0.5,
        "max_response_time_ms": 10.0
    },
    "cache": {
        "cache_size": 150,
        "max_size": 1000,
        "total_requests": 1000,
        "cache_hits": 300,
        "cache_misses": 700,
        "hit_rate_percent": 30.0,
        "ttl_seconds": 3600
    },
    "strategies": {
        "greeting_strategy": {
            "usage_count": 200,
            "success_count": 198,
            "error_count": 2,
            "success_rate_percent": 99.0,
            "average_response_time_ms": 0.8,
            "average_confidence": 0.92
        },
        "requirement_strategy": {
            "usage_count": 150,
            "success_count": 145,
            "error_count": 5,
            "success_rate_percent": 96.7,
            "average_response_time_ms": 2.1,
            "average_confidence": 0.85
        }
    },
    "intents": {
        "greeting": {
            "total_requests": 200,
            "successful_requests": 198,
            "failed_requests": 2,
            "success_rate_percent": 99.0,
            "average_response_time_ms": 0.8
        }
    },
    "hourly": {
        "total_requests": 100,
        "successful_requests": 95,
        "success_rate_percent": 95.0,
        "average_response_time_ms": 1.2,
        "requests_per_minute": 1.67
    },
    "registry": {
        "total_strategies": 6,
        "strategy_names": [
            "greeting_strategy",
            "requirement_strategy",
            "knowledge_base_strategy",
            "capabilities_strategy",
            "emotional_support_strategy",
            "fallback_strategy"
        ]
    }
}
```

### 2. 获取错误记录

```python
# 获取最近的错误记录
errors = engine.get_recent_errors(limit=10)

# 返回结构
[
    {
        "timestamp": "2025-01-15T10:30:00",
        "message": "用户消息内容",
        "intent": "unknown",
        "strategy": "fallback_strategy",
        "error": "策略执行失败: 网络超时",
        "response_time_ms": 5000.0
    }
]
```

### 3. 获取性能趋势

```python
# 获取最近1小时的性能趋势
trends = engine.get_performance_trends(minutes=60)

# 返回结构
{
    "timestamps": [1642248000, 1642248060, 1642248120],  # Unix时间戳
    "response_times": [1.2, 1.5, 1.1],                  # 响应时间(ms)
    "success_rates": [95.0, 92.0, 98.0]                 # 成功率(%)
}
```

## 🔧 管理接口

### 1. 缓存管理

```python
# 清空缓存
engine.clear_cache()

# 获取缓存统计
cache_stats = engine.get_performance_stats()['cache']

# 手动清理过期缓存
from backend.agents.decision_cache import get_decision_cache
cache = get_decision_cache()
expired_count = cache.cleanup_expired()
```

### 2. 监控管理

```python
# 重置监控统计
engine.reset_monitoring()

# 获取监控实例进行高级操作
from backend.agents.decision_monitor import get_decision_monitor
monitor = get_decision_monitor()

# 获取详细统计
overall_stats = monitor.get_overall_stats()
strategy_stats = monitor.get_strategy_stats()
intent_stats = monitor.get_intent_stats()
hourly_stats = monitor.get_hourly_stats()
```

### 3. 适配器管理

```python
# 切换引擎类型
adapter.switch_engine(use_unified=True)   # 使用统一引擎
adapter.switch_engine(use_unified=False)  # 使用传统引擎

# 获取适配器性能指标
metrics = adapter.get_performance_metrics()
# 返回结构
{
    "total_calls": 500,
    "total_time": 2.5,
    "average_time": 0.005,
    "error_count": 5,
    "error_rate": 0.01,
    "engine_type": "unified"
}
```

## 🚨 错误处理

### 常见错误类型

1. **策略执行失败**
   - 错误码: `STRATEGY_EXECUTION_ERROR`
   - 处理: 自动回退到fallback_strategy

2. **上下文分析失败**
   - 错误码: `CONTEXT_ANALYSIS_ERROR`
   - 处理: 使用默认上下文继续处理

3. **缓存操作失败**
   - 错误码: `CACHE_OPERATION_ERROR`
   - 处理: 跳过缓存，直接执行决策

4. **监控记录失败**
   - 错误码: `MONITORING_ERROR`
   - 处理: 记录错误日志，不影响决策流程

### 错误处理示例

```python
try:
    result = await engine.make_decision(context)
except Exception as e:
    # 统一决策引擎内部已处理错误
    # 返回的result包含错误信息
    if result.metadata.get('error'):
        print(f"决策执行出错: {result.metadata['error']}")
    
    # 检查是否为回退结果
    if result.metadata.get('fallback'):
        print("使用了回退策略")
```

## 📈 性能优化建议

### 1. 缓存优化
```python
# 调整缓存参数
from backend.agents.decision_cache import get_decision_cache

cache = get_decision_cache(
    max_size=2000,      # 增加缓存大小
    ttl_seconds=7200    # 延长缓存时间
)
```

### 2. 监控优化
```python
# 调整监控记录数量
from backend.agents.decision_monitor import get_decision_monitor

monitor = get_decision_monitor(max_records=2000)
```

### 3. 批量处理
```python
# 批量决策处理
async def batch_decisions(messages, base_context):
    results = []
    for message in messages:
        context = create_decision_context(
            **base_context,
            message=message
        )
        result = await engine.make_decision(context)
        results.append(result)
    return results
```

## 🔗 相关文档

- [统一决策引擎用户指南](./guides/统一决策引擎用户指南.md)
- [策略开发指南](./guides/策略开发指南.md)
- [性能监控指南](./tools/performance-monitoring.md)
- [统一决策引擎架构设计](../统一决策引擎架构设计.md)
