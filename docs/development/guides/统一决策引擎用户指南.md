# 统一决策引擎用户指南

## 📖 概述

统一决策引擎是需求采集系统的智能核心，负责分析用户输入并选择最合适的处理策略。本指南将帮助您了解如何使用和扩展这个系统。

## 🏗️ 系统架构

### 核心组件

```
统一决策引擎
├── 🎯 UnifiedDecisionEngine (核心引擎)
├── 🔄 DecisionEngineAdapter (兼容适配器)
├── 📋 StrategyRegistry (策略注册中心)
├── 🧠 ContextAnalyzer (上下文分析器)
├── 💾 DecisionCache (决策缓存)
└── 📊 DecisionMonitor (性能监控)
```

### 策略体系

| 优先级 | 策略名称 | 处理场景 | 文件位置 |
|--------|----------|----------|----------|
| 9 | GreetingStrategy | 问候消息 | `strategies/greeting_strategy.py` |
| 8 | RequirementStrategy | 需求收集 | `strategies/requirement_strategy.py` |
| 7 | KnowledgeBaseStrategy | 知识查询 | `strategies/knowledge_base_strategy.py` |
| 6 | CapabilitiesStrategy | 能力介绍 | `strategies/capabilities_strategy.py` |
| 5 | EmotionalSupportStrategy | 情感支持 | `strategies/emotional_support_strategy.py` |
| 1 | FallbackStrategy | 兜底处理 | `strategies/fallback_strategy.py` |

## 🚀 快速开始

### 基本使用

```python
from backend.agents.unified_decision_engine import get_unified_decision_engine
from backend.agents.decision_types import create_decision_context
from backend.agents.unified_state_manager import ConversationState

# 获取决策引擎实例
engine = get_unified_decision_engine()

# 创建决策上下文
context = create_decision_context(
    session_id='user_session_123',
    user_id='user_456',
    message='你好',
    current_state=ConversationState.IDLE
)

# 执行决策
result = await engine.make_decision(context)

# 获取结果
print(f"动作: {result.action}")
print(f"回复: {result.response_template}")
print(f"置信度: {result.confidence}")
```

### 兼容性使用（推荐用于现有代码）

```python
from backend.agents.decision_engine_adapter import get_decision_engine_adapter

# 获取适配器（保持与旧系统100%兼容）
adapter = get_decision_engine_adapter(use_unified_engine=True)

# 使用旧格式的上下文
context = [{
    'session_id': 'user_session_123',
    'user_id': 'user_456',
    'current_state': 'IDLE',
    'conversation_history': []
}]

# 执行决策（与旧系统相同的接口）
result = await adapter.analyze('你好', context)

# 结果格式与旧系统完全一致
decision = result['decision']
print(f"动作: {decision['action']}")
print(f"意图: {result['intent']}")
```

## 📊 性能监控

### 获取性能统计

```python
# 获取完整的性能统计
stats = engine.get_performance_stats()

# 缓存统计
cache_stats = stats.get('cache', {})
print(f"缓存命中率: {cache_stats.get('hit_rate_percent', 0)}%")

# 整体性能
overall_stats = stats.get('overall', {})
print(f"平均响应时间: {overall_stats.get('average_response_time_ms', 0)}ms")
print(f"成功率: {overall_stats.get('success_rate_percent', 0)}%")

# 策略使用情况
strategy_stats = stats.get('strategies', {})
for strategy_name, metrics in strategy_stats.items():
    print(f"{strategy_name}: 使用{metrics['usage_count']}次")
```

### 获取错误信息

```python
# 获取最近的错误记录
errors = engine.get_recent_errors(limit=5)
for error in errors:
    print(f"时间: {error['timestamp']}")
    print(f"错误: {error['error']}")
    print(f"消息: {error['message']}")
```

### 获取性能趋势

```python
# 获取最近1小时的性能趋势
trends = engine.get_performance_trends(minutes=60)
print(f"时间点数: {len(trends['timestamps'])}")
print(f"响应时间趋势: {trends['response_times']}")
print(f"成功率趋势: {trends['success_rates']}")
```

## 🔧 系统配置

### 缓存配置

```python
from backend.agents.decision_cache import get_decision_cache

# 获取缓存实例
cache = get_decision_cache(
    max_size=1000,      # 最大缓存条目数
    ttl_seconds=3600    # 缓存生存时间（1小时）
)

# 获取缓存统计
cache_stats = cache.get_stats()
print(f"缓存大小: {cache_stats['cache_size']}")
print(f"命中率: {cache_stats['hit_rate_percent']}%")

# 清空缓存
cache.clear()
```

### 监控配置

```python
from backend.agents.decision_monitor import get_decision_monitor

# 获取监控实例
monitor = get_decision_monitor(max_records=1000)

# 获取监控统计
overall_stats = monitor.get_overall_stats()
strategy_stats = monitor.get_strategy_stats()
hourly_stats = monitor.get_hourly_stats()

# 重置统计
monitor.reset_stats()
```

## 🛠️ 故障排除

### 常见问题

#### 1. 决策响应慢
```python
# 检查缓存命中率
stats = engine.get_performance_stats()
cache_hit_rate = stats.get('cache', {}).get('hit_rate_percent', 0)

if cache_hit_rate < 60:
    print("缓存命中率过低，考虑调整缓存策略")
    
# 检查策略执行时间
strategy_stats = stats.get('strategies', {})
for name, metrics in strategy_stats.items():
    if metrics['average_response_time_ms'] > 100:
        print(f"策略 {name} 响应时间过长: {metrics['average_response_time_ms']}ms")
```

#### 2. 策略匹配不准确
```python
# 检查策略使用分布
strategy_stats = stats.get('strategies', {})
fallback_usage = strategy_stats.get('fallback_strategy', {}).get('usage_count', 0)
total_requests = stats.get('overall', {}).get('total_requests', 1)

fallback_rate = (fallback_usage / total_requests) * 100
if fallback_rate > 20:
    print(f"回退策略使用率过高: {fallback_rate:.1f}%")
    print("建议检查策略匹配逻辑或添加新策略")
```

#### 3. 内存使用过高
```python
# 检查缓存大小
cache_stats = stats.get('cache', {})
cache_size = cache_stats.get('cache_size', 0)
max_size = cache_stats.get('max_size', 1000)

if cache_size > max_size * 0.9:
    print("缓存接近满载，考虑增加缓存大小或减少TTL")
    
# 清理过期缓存
cache = get_decision_cache()
expired_count = cache.cleanup_expired()
print(f"清理了 {expired_count} 个过期缓存条目")
```

### 调试模式

```python
import logging

# 启用调试日志
logging.getLogger('backend.agents').setLevel(logging.DEBUG)

# 执行决策并查看详细日志
result = await engine.make_decision(context)
```

## 📈 性能优化建议

### 1. 缓存优化
- 合理设置缓存大小（建议1000-5000条目）
- 根据业务场景调整TTL（建议1-6小时）
- 定期清理过期缓存

### 2. 策略优化
- 将高频使用的策略设置更高优先级
- 优化策略的`can_handle`方法，减少不必要的计算
- 使用更精确的关键词匹配

### 3. 监控优化
- 定期查看性能趋势，识别性能瓶颈
- 监控错误率，及时发现问题
- 根据使用情况调整策略优先级

## 🔗 相关文档

- [统一决策引擎架构设计](../统一决策引擎架构设计.md)
- [统一决策引擎实施计划](../统一决策引擎实施计划.md)
- [策略开发指南](./策略开发指南.md)
- [性能监控指南](../tools/performance-monitoring.md)

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本指南的故障排除部分
2. 检查系统日志和监控数据
3. 参考相关技术文档
4. 联系开发团队获取支持
