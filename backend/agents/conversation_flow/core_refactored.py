"""
对话流程核心模块 - 重构版本

AutoGenConversationFlowAgent的重构实现，提供：
- 依赖注入架构，提高可测试性和可维护性
- 三层意图识别系统（关键词、语义、LLM）
- 状态驱动的对话流程管理
- 需求采集和文档生成功能
- 统一配置驱动的业务逻辑

主要功能：
1. 消息处理和意图识别
2. 状态管理和流程控制
3. 需求采集和关注点管理
4. 文档生成和确认流程
5. 知识库查询和回复生成
"""

# 导入必要模块
from backend.utils.common_imports import get_logger
from typing import Any, Dict, List, Optional, Union, Callable
import asyncio
import json
import time
from datetime import datetime

# 导入项目模块 - 使用接口而非具体实现
from ..base import AutoGenBaseAgent
from ..conversation_flow_reply_mixin import ConversationFlowReplyMixin
from ..conversation_flow_message_mixin import ConversationFlowMessageMixin
from ..session_context import SessionContextManager
from backend.agents.decision_engine_adapter import get_decision_engine_adapter
from backend.handlers.action_executor_interface import ConversationFlowInterface

# 使用统一配置接口
from backend.config.unified_config_loader import get_unified_config

# 导入模块组件
from .utils import ConversationConstants
from backend.utils.progress_indicator import ProgressIndicator, ProgressStage


class AutoGenConversationFlowAgent(AutoGenBaseAgent, ConversationFlowReplyMixin, ConversationFlowMessageMixin, ConversationFlowInterface):
    """
    AutoGen对话流程管理Agent - 依赖注入版本
    
    核心改进：
    - 依赖注入：通过构造函数注入所有依赖，而非内部创建
    - 接口分离：依赖抽象接口而非具体实现
    - 配置驱动：使用统一配置服务
    - 可测试性：易于模拟和单元测试
    - 松耦合：减少模块间的直接依赖
    """
    
    def __init__(self,
                 # 核心依赖（必需）
                 session_id: str,
                 config_service: Any,
                 llm_service: Any,
                 
                 # 数据管理依赖
                 message_manager: Any,
                 document_manager: Any,
                 focus_point_manager: Any,
                 database_manager: Any,
                 
                 # Agent依赖
                 document_generator: Any,
                 knowledge_base_agent: Any,
                 # 组件依赖
                 state_manager: Any,
                 session_manager: Any,
                 prompt_loader: Any,

                 # 可选依赖
                 intent_decision_engine: Any = None,
                 
                 # 可选依赖（message_processor改为可选以解决循环依赖）
                 message_processor: Any = None,
                 
                 # 可选依赖
                 information_extractor_agent: Any = None,
                 review_and_refine_agent: Any = None,
                 domain_classifier_agent: Any = None,
                 category_classifier_agent: Any = None,

                 # 混合AI代理依赖（可选）
                 hybrid_conversation_router: Any = None,
                 knowledge_base_config_manager: Any = None,
                 
                 # AutoGen配置参数
                 system_message: str = None,
                 is_termination_msg: Callable = None,
                 max_consecutive_auto_reply: int = None,
                 human_input_mode: str = None,
                 code_execution_config: Dict = None,
                 llm_config: Dict = None,
                 default_auto_reply: str = None,
                 
                 # 回复系统依赖（可选）
                 reply_manager: Any = None,
                 reply_factory: Any = None,
                 integrated_reply_system: Any = None,
                 version_manager: Any = None,
                 
                 **kwargs):
        """
        初始化ConversationFlowAgent
        
        Args:
            session_id: 会话ID
            config_service: 配置服务
            llm_service: LLM服务
            message_manager: 消息管理器
            document_manager: 文档管理器
            focus_point_manager: 焦点管理器
            database_manager: 数据库管理器
            document_generator: 文档生成器
            knowledge_base_agent: 知识库Agent
            intent_decision_engine: 意图决策引擎
            state_manager: 状态管理器
            session_manager: 会话管理器
            message_processor: 消息处理器
            prompt_loader: 提示加载器
            **kwargs: 其他参数
        """
        
        self.logger = get_logger(f"{__name__}.{session_id}")
        self.session_id = session_id
        
        # 注入核心依赖
        self.config_service = config_service
        self.config_manager = config_service  # 向后兼容别名
        self.llm_service = llm_service
        self.llm_client = llm_service  # 向后兼容别名
        
        # 注入数据管理依赖
        self.message_manager = message_manager
        self.document_manager = document_manager
        self.focus_point_manager = focus_point_manager
        self.database_manager = database_manager
        self.db_manager = database_manager  # 向后兼容别名
        
        # 注入Agent依赖
        self.document_generator_agent = document_generator
        self.document_generator = document_generator  # 向后兼容别名
        self.knowledge_base_agent = knowledge_base_agent
        self.intent_decision_engine = intent_decision_engine or get_decision_engine_adapter(use_unified_engine=True)
        
        # 注入组件依赖
        self.state_manager = state_manager
        self.session_manager = session_manager  
        self.message_processor = message_processor
        self.prompt_loader = prompt_loader
        
        # 注入可选依赖
        self.information_extractor_agent = information_extractor_agent
        self.review_and_refine_agent = review_and_refine_agent
        self.domain_classifier_agent = domain_classifier_agent
        self.category_classifier_agent = category_classifier_agent

        # 注入混合AI代理依赖
        self.hybrid_conversation_router = hybrid_conversation_router
        self.knowledge_base_config_manager = knowledge_base_config_manager

        # 注入回复系统依赖
        self.reply_manager = reply_manager
        self.reply_factory = reply_factory
        self.integrated_reply_system = integrated_reply_system
        self.version_manager = version_manager

        # 初始化统一配置加载器
        self.unified_config_loader = get_unified_config()
        
        # 获取LLM配置
        if llm_config is None:
            llm_config = self.config_service.get_llm_config("conversation_flow")
        self.llm_config = llm_config
        
        # 构建AutoGen ConversableAgent配置
        conversable_agent_kwargs = {
            'name': f"ConversationFlow_{session_id}",
            'system_message': system_message or self._get_default_system_message(),
            'is_termination_msg': is_termination_msg,
            'max_consecutive_auto_reply': max_consecutive_auto_reply or 1,
            'human_input_mode': human_input_mode or "NEVER",
            'code_execution_config': code_execution_config or {'use_docker': False},
            'llm_config': llm_config,
            'default_auto_reply': default_auto_reply,
        }
        
        # 初始化父类
        super().__init__(**conversable_agent_kwargs)
        
        # 初始化会话上下文管理器
        self.session_context_manager = SessionContextManager(self.database_manager)
        
        # 初始化历史服务
        from backend.services.conversation_history_service import get_history_service
        self.history_service = get_history_service(self.message_manager)

        # 初始化摘要管理器
        from backend.data.db.summary_manager import SummaryManager
        self.summary_manager = SummaryManager(self.database_manager)

        # 🔧 先初始化回复系统组件（来自ConversationFlowReplyMixin）
        self.logger.info("开始初始化回复系统组件...")
        self._initialize_reply_systems()

        # 然后初始化Action执行器（确保能获取到reply_factory）
        self._initialize_action_executor()

        # 初始化完成日志
        self.logger.info(f"ConversationFlowAgent初始化完成: {session_id}")

    def get_unified_config(self):
        """获取统一配置加载器"""
        return self.unified_config_loader

    def _get_default_system_message(self) -> str:
        """获取默认系统消息"""
        try:
            return self.config_service.get_message_template(
                "system.welcome",
                agent_name="ConversationFlow"
            )
        except Exception as e:
            self.logger.warning(f"获取默认系统消息失败: {e}")
            return "我是AI需求采集助手，专门帮助您整理和分析业务需求。"
    
    def _initialize_action_executor(self) -> None:
        """初始化Action执行器 - 条件初始化避免重复"""
        # 检查是否已经有ActionExecutor实例（可能由MessageProcessor设置）
        if hasattr(self, 'action_executor') and self.action_executor is not None:
            self.logger.debug("ActionExecutor已存在，跳过初始化")
            return

        try:
            from backend.handlers.action_executor import ActionExecutor
            self.action_executor = ActionExecutor(
                conversation_flow=self  # 修正参数名
            )
            self.logger.debug("ActionExecutor初始化成功")
        except Exception as e:
            self.logger.error(f"ActionExecutor初始化失败: {e}")
            self.action_executor = None
    
    # ==================== 核心业务方法 ====================
    
    async def process_message_async(self, message: str, sender_name: str = "user", progress_callback=None) -> Dict[str, Any]:
        """
        异步处理消息

        Args:
            message: 用户消息
            sender_name: 发送者名称
            progress_callback: 进度更新回调函数

        Returns:
            处理结果字典
        """
        try:
            # 确保消息是字符串并安全切片
            message_str = str(message) if message is not None else ""
            self.logger.info(f"开始异步处理消息: {message_str[:50]}...")

            # 统一使用 MessageProcessor + SimplifiedDecisionEngine 架构
            # 移除 HybridConversationRouter 的复杂路由逻辑，简化架构

            # 检查MessageProcessor是否可用
            processor_available = (
                self.message_processor and 
                hasattr(self.message_processor, 'process_message') and
                hasattr(self.message_processor, 'session_context_manager') and
                self.message_processor.session_context_manager is not None
            )
            
            if processor_available:
                try:
                    # 尝试使用MessageProcessor
                    # 根据MessageProcessor.process_message()的签名，第一个参数是message字符串
                    result = await self.message_processor.process_message(
                        message_str,  # 第一个位置参数：消息字符串
                        session_context=None,  # 第二个参数：会话上下文
                        session_id=self.session_id,  # 通过kwargs传递额外参数
                        user_id=self.session_id,
                        progress_callback=progress_callback  # 传递进度回调
                    )
                    self.logger.info(f"MessageProcessor处理成功")
                    return result
                except Exception as mp_error:
                    self.logger.warning(f"MessageProcessor处理失败，使用简化处理: {mp_error}")
                    # 继续到简化处理
            else:
                # MessageProcessor不可用，使用简化处理
                pass

            # 简化处理逻辑
            result = await self._simple_message_processing(message_str, sender_name)
            self.logger.info(f"消息处理完成: {self.session_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"异步消息处理失败: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "reply": self.config_service.get_message_template("error.system")
            }

    async def _process_with_hybrid_router(self, message: str, sender_name: str) -> Dict[str, Any]:
        """
        使用混合对话路由器处理消息

        Args:
            message: 用户消息
            sender_name: 发送者名称

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 获取当前会话状态
            current_state = "IDLE"  # 默认状态
            try:
                # 从会话上下文管理器获取当前状态
                session_ctx = await self.session_context_manager.load_session_context(
                    self.session_id, self.session_id
                )
                if session_ctx and hasattr(session_ctx, 'current_state'):
                    current_state = session_ctx.current_state.name
            except Exception as e:
                self.logger.info(f"获取会话状态失败，使用默认状态: {e}")

            # 构建会话上下文
            session_context = {
                "session_id": self.session_id,
                "user_id": self.session_id,  # 简化处理，使用session_id作为user_id
                "sender_name": sender_name,
                "current_mode": getattr(self, '_current_mode', None),
                "conversation_history": await self._get_conversation_history(),
                "current_state": current_state  # 修复：传递正确的状态字符串
            }

            # 调用混合对话路由器
            routing_result = await self.hybrid_conversation_router.route_message(
                message=message,
                session_context=session_context
            )

            if routing_result.success:
                # 更新会话状态
                if routing_result.context_updates:
                    self._update_session_context(routing_result.context_updates)

                return {
                    "success": True,
                    "reply": routing_result.content,
                    "mode": routing_result.mode.value,
                    "processing_info": routing_result.processing_info,
                    "context_updates": routing_result.context_updates
                }
            else:
                # 路由失败，回退到原有逻辑
                self.logger.warning(f"混合路由器处理失败: {routing_result.error}")
                return await self._simple_message_processing(message, sender_name)

        except Exception as e:
            self.logger.error(f"混合路由器处理异常: {e}", exc_info=True)
            # 异常时回退到原有逻辑
            return await self._simple_message_processing(message, sender_name)

    async def _get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        try:
            if hasattr(self, 'history_service') and self.history_service:
                return await self.history_service.get_recent_messages(
                    session_id=self.session_id,
                    user_id=self.session_id,  # 使用session_id作为user_id，与其他地方保持一致
                    limit=10
                )
            return []
        except Exception as e:
            self.logger.error(f"获取对话历史失败: {e}")
            return []

    def _update_session_context(self, context_updates: Dict[str, Any]):
        """更新会话上下文"""
        try:
            for key, value in context_updates.items():
                setattr(self, f'_{key}', value)
            self.logger.debug(f"会话上下文已更新: {list(context_updates.keys())}")
        except Exception as e:
            self.logger.error(f"更新会话上下文失败: {e}")

    async def _simple_message_processing(self, message: str, sender_name: str) -> Dict[str, Any]:
        """
        优化后的消息处理：使用完整的三层匹配决策引擎
        当MessageProcessor不可用时的回退方案，但保持完整的意图识别能力
        """
        try:
            self.logger.info(f"开始三层匹配消息处理: {message[:50]}...")
            
            # 使用完整的三层匹配意图识别
            if self.intent_decision_engine:
                self.logger.debug("使用SimplifiedDecisionEngine进行决策")

                # 获取当前会话状态
                current_state = "IDLE"  # 默认状态
                try:
                    session_ctx = await self.session_context_manager.load_session_context(
                        self.session_id, self.session_id
                    )
                    if session_ctx and hasattr(session_ctx, 'current_state'):
                        current_state = session_ctx.current_state.name
                except Exception as e:
                    self.logger.info(f"获取会话状态失败，使用默认状态: {e}")

                # 使用完整的意图决策引擎进行三层匹配
                context = [{
                    "session_id": self.session_id,
                    "current_state": current_state,  # 修复：传递正确的状态字符串
                    "conversation_history": getattr(self, 'conversation_history', []),
                    "user_profile": getattr(self, 'user_profile', {}),
                    "business_context": getattr(self, 'business_context', {})
                }]
                decision_result = await self.intent_decision_engine.analyze(message, context)
                action = decision_result.get("recommended_action", "handle_general_request")
                # 获取决策结果
            else:
                self.logger.warning("未找到意图决策引擎，使用简化fallback逻辑")
                # Fallback到简化逻辑（仅在引擎不可用时使用）
                decision_result = await self._simple_intent_decision(message)
                action = decision_result.get("recommended_action", "handle_general_request")
            
            # 根据action生成回复
            self.logger.debug(f"执行action: {action}")
            if action == "explain_capabilities":
                reply = await self.handle_explain_capabilities(message, self.session_id, {})
            elif action == "provide_help_guidance":
                reply = await self.handle_help_guidance(message, self.session_id, {})
            elif action == "respond_to_general_chat":
                reply = await self.handle_general_chat(message, self.session_id, {})
            elif action == "start_requirement_gathering":
                reply = await self.handle_start_requirement_gathering(message, self.session_id, {})
            elif action == "reset_conversation":
                reply = await self.handle_reset_conversation(message, self.session_id, {})
            else:
                reply = await self.handle_general_request(message, self.session_id, {})
            
            # 生成回复完成
            
            result = {
                "success": True,
                "reply": reply,
                "action": action,
                "session_id": self.session_id
            }
            
            self.logger.info(f"三层匹配消息处理成功: action={action}")
            return result
            
        except Exception as e:
            self.logger.error(f"三层匹配消息处理失败: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "reply": "抱歉，我遇到了一些技术问题。请稍后再试。",
                "session_id": self.session_id
            }
    
    async def _simple_intent_decision(self, message: str) -> Dict[str, Any]:
        """
        基于配置文件的三层意图识别逻辑
        
        使用 business_rules.yaml 中的配置，而不是硬编码关键词
        """
        try:
            message_lower = message.lower().strip()
            
            # 优先使用配置文件中的快速意图识别
            quick_intent_result = await self._check_quick_intent_rules(message_lower)
            if quick_intent_result:
                return quick_intent_result
            
            # Layer 3: 需求采集类 - 默认处理
            # 检查是否是需求相关的内容（包含具体业务描述）
            business_indicators = [
                "想要", "需要", "希望", "计划", "打算", "要做", "想做",
                "项目", "业务", "系统", "产品", "服务", "流程",
                "问题", "困难", "挑战", "目标", "需求", "开发", "设计", "建设"
            ]
            
            has_business_content = any(indicator in message_lower for indicator in business_indicators)
            is_substantial_message = len(message_lower) > 10  # 排除过短的消息
            
            if has_business_content or is_substantial_message:
                return {"recommended_action": "start_requirement_gathering", "confidence": 0.8}
            else:
                # 简短的非业务消息，按一般聊天处理
                return {"recommended_action": "respond_to_general_chat", "confidence": 0.7}
                
        except Exception as e:
            self.logger.error(f"简化意图识别失败: {e}")
            # 出错时默认走需求采集流程
            return {"recommended_action": "start_requirement_gathering", "confidence": 0.5}
    
    async def _check_quick_intent_rules(self, message_lower: str) -> Optional[Dict[str, Any]]:
        """
        检查快速意图识别规则（基于配置文件）
        """
        try:
            # 获取配置文件中的快速意图规则
            quick_rules = self.config_service.get_business_rule("conversation.keyword_acceleration.rules", [])
            
            if not quick_rules:
                self.logger.debug("未找到快速意图规则配置")
                return None
            
            # 按优先级排序检查
            sorted_rules = sorted(quick_rules, key=lambda x: x.get("priority", 0), reverse=True)
            
            for rule in sorted_rules:
                intent = rule.get("intent")
                keywords_config_key = rule.get("keywords_config_key")
                confidence = rule.get("confidence", 0.95)
                
                if not intent or not keywords_config_key:
                    continue
                
                # 获取关键词列表
                keywords = self._get_keywords_from_config_key(keywords_config_key)
                if not keywords:
                    continue
                
                # 检查是否匹配
                if any(keyword in message_lower for keyword in keywords):
                    action = self._map_intent_to_action(intent)
                    return {
                        "recommended_action": action,
                        "confidence": confidence,
                        "matched_intent": intent,
                        "matched_rule": keywords_config_key
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"检查快速意图规则失败: {e}")
            return None
    
    def _get_keywords_from_config_key(self, config_key: str) -> List[str]:
        """
        根据配置键获取关键词列表
        
        例如: "simple_commands.greeting_keywords" -> ["你好", "hi", ...]
        """
        try:
            return self.config_service.get_business_rule(config_key, [])
        except Exception as e:
            self.logger.error(f"获取关键词配置失败: {config_key}, 错误: {e}")
            return []
    
    def _map_intent_to_action(self, intent: str) -> str:
        """
        将意图映射为动作
        
        Args:
            intent: 配置文件中定义的意图（如 "greeting", "restart"）
            
        Returns:
            对应的动作名称
        """
        intent_action_mapping = {
            "greeting": "respond_to_general_chat",
            "restart": "reset_conversation", 
            "modify": "handle_general_request",  # 修改请求暂时映射到通用处理
            "confirm": "handle_general_request",  # 确认请求暂时映射到通用处理
            "system_capability_query": "explain_capabilities",
        }
        
        return intent_action_mapping.get(intent, "start_requirement_gathering")
    
    async def process_message(self,
                            message: str,
                            session_id: str,
                            user_id: str,
                            **kwargs) -> Dict[str, Any]:
        """
        处理用户消息 - 实现ConversationFlowInterface接口

        Args:
            message: 用户消息
            session_id: 会话ID
            user_id: 用户ID
            **kwargs: 其他参数

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 如果是当前会话，使用现有的异步处理方法
            if session_id == self.session_id:
                return await self.process_message_async(
                    message,
                    kwargs.get('sender_name', 'user'),
                    progress_callback=kwargs.get('progress_callback')
                )
            else:
                # 如果是其他会话，返回错误信息
                return {
                    "success": False,
                    "error": f"Cannot process message for different session. Current: {self.session_id}, Requested: {session_id}",
                    "reply": "抱歉，无法处理其他会话的消息。"
                }
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "reply": "抱歉，处理消息时遇到了问题。"
            }

    def process_message_sync(self, message: Union[str, Dict[str, Any]], sender_name: str = "user") -> Dict[str, Any]:
        """
        同步处理消息（保持向后兼容）

        Args:
            message: 用户消息（字符串或字典格式）
            sender_name: 发送者名称

        Returns:
            处理结果字典
        """
        try:
            # 处理不同的输入格式
            if isinstance(message, dict):
                message_text = message.get("text", str(message))
                if "session_id" in message:
                    # 如果字典中包含session_id，可以考虑使用它
                    pass
            else:
                message_text = str(message) if message is not None else ""

            # 检查是否在事件循环中
            try:
                asyncio.get_running_loop()
                # 如果已经在事件循环中，创建一个任务并等待完成
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(self._run_async_in_thread, message_text, sender_name)
                    return future.result()
            except RuntimeError:
                # 没有运行的事件循环，可以安全使用asyncio.run()
                return asyncio.run(self.process_message_async(message_text, sender_name))
        except Exception as e:
            self.logger.error(f"同步消息处理失败: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "reply": self.config_service.get_message_template("error.system")
            }
    
    def _run_async_in_thread(self, message_text: str, sender_name: str) -> Dict[str, Any]:
        """在新线程中运行异步函数"""
        import asyncio
        return asyncio.run(self.process_message_async(message_text, sender_name))
    
    def get_conversation_state(self) -> Dict[str, Any]:
        """获取会话状态"""
        try:
            return self.state_manager.get_conversation_state(self.session_id)
        except Exception as e:
            self.logger.error(f"获取会话状态失败: {e}")
            return {"state": "UNKNOWN", "error": str(e)}

    def get_session_context(self, session_id: str, user_id: str) -> Any:
        """
        获取会话上下文 - 实现ConversationFlowInterface接口

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Any: 会话上下文对象
        """
        try:
            # 如果请求的是当前会话的上下文，直接返回session_context_manager
            if session_id == self.session_id:
                return self.session_context_manager
            else:
                # 如果请求的是其他会话的上下文，创建新的管理器
                from ..session_context import SessionContextManager
                return SessionContextManager(self.database_manager)
        except Exception as e:
            self.logger.error(f"获取会话上下文失败: {e}")
            return None
    
    def reset_conversation(self) -> bool:
        """重置会话"""
        try:
            self.logger.info(f"重置会话: {self.session_id}")
            
            # 重置各个组件的状态
            if hasattr(self.session_manager, 'reset_session'):
                self.session_manager.reset_session(self.session_id)
            
            if hasattr(self.state_manager, 'reset_state'):
                self.state_manager.reset_state(self.session_id)
            
            self.logger.info(f"会话重置完成: {self.session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"重置会话失败: {e}")
            return False
    
    # ==================== 向后兼容方法 ====================
    
    def generate_reply(self, messages: List[Dict], sender, config=None) -> str:
        """生成回复（AutoGen接口兼容）"""
        try:
            if not messages:
                return self.config_service.get_message_template("greeting.basic")
            
            last_message = messages[-1]
            message_content = last_message.get("content", "")
            
            # 使用新的消息处理流程
            result = self.process_message(message_content)
            return result.get("reply", self.config_service.get_message_template("error.system"))
            
        except Exception as e:
            self.logger.error(f"生成回复失败: {e}")
            return self.config_service.get_message_template("error.system")
    
    # ==================== 业务处理方法（兼容性） ====================
    
    async def handle_explain_capabilities(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理能力解释请求"""
        try:
            # 使用硬编码的能力介绍，避免依赖不存在的模板
            capability_response = """我是AI需求采集助手，专门帮助您整理和分析业务需求。我的主要能力包括：

🎯 **需求收集**：帮助您梳理和整理业务需求，确保需求的完整性和准确性

📋 **需求分析**：对收集到的需求进行分类、优先级排序和可行性分析

📝 **文档生成**：自动生成规范的需求文档，包括功能规格说明等

💡 **智能建议**：基于行业经验提供优化建议和最佳实践

🔄 **交互式对话**：通过自然语言对话的方式，让需求收集过程更加高效和友好

有什么具体问题我可以帮助您解答吗？"""
            
            self.logger.info(f"能力解释生成完成: {session_id}")
            return capability_response
            
        except Exception as e:
            self.logger.error(f"处理能力解释失败: {e}")
            return "很抱歉，我在处理您的请求时遇到了问题。我是AI需求采集助手，可以帮助您整理业务需求。请告诉我您需要什么帮助？"
    
    async def handle_general_request(self, message: str, session_id: str, 
                                   decision_result: Dict[str, Any], 
                                   history: List[Dict[str, Any]] = None,
                                   action_command: str = None) -> str:
        """处理一般请求（兼容性方法）"""
        try:
            # 避免递归调用，直接返回固定回复
            return "您好！我是AI需求采集助手，可以帮助您整理和分析业务需求。请告诉我您想要咨询什么内容？"
            
        except Exception as e:
            self.logger.error(f"处理一般请求失败: {e}")
            return "抱歉，我暂时无法处理您的请求。请稍后再试。"
    
    async def handle_help_guidance(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理帮助指导请求"""
        try:
            help_response = """我来帮助您！作为AI需求采集助手，我可以协助您：

📝 **开始需求收集**：告诉我您的项目背景，我会引导您逐步梳理需求
🔍 **分析现有需求**：如果您已有部分需求，我可以帮您完善和分析
📊 **需求优先级排序**：帮助您确定需求的重要性和紧急程度
📋 **生成需求文档**：整理讨论结果，生成标准的需求文档
❓ **解答疑问**：关于需求分析的任何问题，我都很乐意帮助

请告诉我您想从哪里开始？"""
            
            return help_response
            
        except Exception as e:
            self.logger.error(f"处理帮助指导失败: {e}")
            return "我很乐意为您提供帮助！请告诉我您在需求收集方面需要什么支持？"
    
    async def handle_start_requirement_gathering(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理需求采集开始"""
        try:
            # 根据用户消息内容，提供针对性的需求采集引导
            response = f"""感谢您提供的信息！我来帮助您整理这个需求。

基于您描述的内容："{message[:100]}..."

让我们从以下几个方面来详细梳理您的需求：

**📋 基本信息确认**
1. 项目/业务的核心目标是什么？
2. 预期要解决的主要问题是什么？
3. 目标用户群体的特征？

**💰 资源和约束**
1. 预算范围大概是多少？
2. 时间期望（什么时候需要完成）？
3. 团队资源情况？

**🎯 期望成果**
1. 您希望达到什么具体效果？
2. 如何衡量项目是否成功？

请先从第一个方面开始，详细描述一下您项目的核心目标。这样我可以更好地帮您制定详细的解决方案。"""
            
            return response
            
        except Exception as e:
            self.logger.error(f"处理需求采集开始失败: {e}")
            return "我很乐意帮您整理需求！请详细描述一下您的项目背景和希望达到的目标，这样我就能为您提供更精准的指导。"
    
    async def _process_intent(self, message: str, session_id: str, user_id: str, decision_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理意图的适配器方法 - 执行完整的需求采集流程

        这个方法作为新旧接口的桥梁，兼容RequirementHandler的调用期望
        """
        try:
            # 获取action类型
            action = decision_result.get('action', 'start_requirement_gathering')

            # 根据action调用相应的处理方法
            if action == "start_requirement_gathering":
                return await self._start_complete_requirement_gathering(message, session_id, user_id, decision_result)
            elif action == "continue_requirement_gathering":
                return await self._continue_complete_requirement_gathering(message, session_id, user_id, decision_result)
            else:
                content = await self.handle_general_request(message, session_id, decision_result)
                return {
                    "content": content,
                    "domain_result": None,
                    "category_result": None,
                    "focus_points_status": None
                }

        except Exception as e:
            self.logger.error(f"_process_intent处理失败: {e}", exc_info=True)
            return {
                "content": "我很乐意帮您整理需求！请详细描述一下您的项目背景和希望达到的目标。",
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None
            }

    async def _start_complete_requirement_gathering(self, message: str, session_id: str, user_id: str, decision_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        启动完整的需求采集流程

        包括：领域分类 -> 类别分类 -> 关注点初始化 -> 生成第一个问题
        """
        try:
            self.logger.info(f"开始完整需求采集流程 - session: {session_id}, user: {user_id}")

            # 0. 检查是否是澄清回答
            clarification_response = await self._handle_clarification_response(message, session_id, user_id)
            if clarification_response:
                self.logger.info(f"处理澄清回答完成 - session: {session_id}")
                return clarification_response

            # 1. 领域分类
            domain_result = await self._perform_domain_classification(message, session_id, user_id)
            self.logger.info(f"领域分类结果: {domain_result}")

            # 2. 检查是否需要领域澄清
            if domain_result and domain_result.get("domain_id") == "LY_100":
                # 领域分类失败，进入澄清引导
                self.logger.info(f"领域分类失败，进入澄清引导 - session: {session_id}")
                return await self._handle_domain_classification_failure(message, session_id, user_id)

            # 3. 类别分类（如果领域分类成功）
            category_result = None
            if domain_result and domain_result.get("status") == "completed":
                category_result = await self._perform_category_classification(message, domain_result, session_id, user_id)
                self.logger.info(f"类别分类结果: {category_result}")

            # 4. 关注点初始化和问题生成
            focus_points_status = None
            content = None

            if category_result and category_result.get("category_id"):
                # 获取关注点并初始化
                focus_points = await self._get_focus_points_for_category(domain_result.get("domain_id"), category_result.get("category_id"))
                if focus_points:
                    # 初始化关注点状态
                    await self.initialize_focus_points(session_id, user_id, focus_points)

                    # 生成第一个问题（包含信息提取流程）
                    user_emotion = decision_result.get("emotion", "neutral")
                    content = await self._generate_first_collection_question(message, domain_result, category_result, focus_points, session_id, user_id, user_emotion)
                    focus_points_status = {"initialized": True, "total_points": len(focus_points)}

                    # 🔥 关键修复：保存领域、类别信息并将状态从IDLE转换为COLLECTING_INFO
                    await self._save_domain_category_and_update_state(session_id, user_id, domain_result, category_result, "COLLECTING_INFO")
                    self.logger.info(f"状态已更新为COLLECTING_INFO - session: {session_id}")
                else:
                    content = f"感谢您的信息！我已识别这是关于{domain_result.get('domain_name', '未知领域')}的{category_result.get('category_name', '需求')}。让我来帮您详细梳理相关需求。\n\n请详细描述一下您的具体需求和期望目标。"
            else:
                # 如果分类失败，返回通用引导
                content = await self.handle_start_requirement_gathering(message, session_id, decision_result)

            return {
                "content": content,
                "domain_result": domain_result,
                "category_result": category_result,
                "focus_points_status": focus_points_status
            }

        except Exception as e:
            self.logger.error(f"完整需求采集流程失败: {e}", exc_info=True)
            # 回退到简单引导
            content = await self.handle_start_requirement_gathering(message, session_id, decision_result)
            return {
                "content": content,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None
            }

    async def _continue_complete_requirement_gathering(self, message: str, session_id: str, user_id: str, decision_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        继续需求采集流程
        """
        # 暂时复用开始流程的逻辑
        return await self._start_complete_requirement_gathering(message, session_id, user_id, decision_result)

    async def _perform_domain_classification(self, message: str, session_id: str, user_id: str) -> Dict[str, Any]:
        """
        执行领域分类
        """
        try:
            if not hasattr(self, 'domain_classifier_agent') or not self.domain_classifier_agent:
                self.logger.warning("领域分类器未初始化，跳过领域分类")
                return None

            # 获取可用领域列表
            domains = await self._get_available_domains()

            # 执行分类（传递session信息以支持进度提示）
            result = await self.domain_classifier_agent.classify(
                text=message,
                domains=domains,
                conversation_history=None,
                session_id=session_id,
                user_id=user_id
            )

            return result

        except Exception as e:
            self.logger.error(f"领域分类失败: {e}", exc_info=True)
            return None

    async def _perform_category_classification(self, message: str, domain_result: Dict[str, Any], session_id: str, user_id: str) -> Dict[str, Any]:
        """
        执行类别分类
        """
        try:
            if not hasattr(self, 'category_classifier_agent') or not self.category_classifier_agent:
                self.logger.warning("类别分类器未初始化，跳过类别分类")
                return None

            # 获取该领域的可用类别列表
            domain_id = domain_result.get("domain_id")
            categories = await self._get_categories_for_domain(domain_id)

            # 执行分类（传递session信息以支持进度提示）
            result = await self.category_classifier_agent.classify(
                text=message,
                categories=categories,
                conversation_history=None,
                session_id=session_id,
                user_id=user_id
            )

            return result

        except Exception as e:
            self.logger.error(f"类别分类失败: {e}", exc_info=True)
            return None

    async def _handle_domain_classification_failure(self, message: str, session_id: str, user_id: str) -> Dict[str, Any]:
        """
        处理领域分类失败的专门澄清引导

        Args:
            message: 用户原始消息
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 澄清引导响应
        """
        try:
            self.logger.info(f"开始处理领域分类失败澄清 - session: {session_id}, message: {message[:50]}...")

            # 生成领域澄清问题
            clarification_content = await self._generate_domain_clarification_question(message)

            # 保存澄清状态到会话上下文
            await self._save_clarification_state(session_id, user_id, "domain_clarification", message)

            return {
                "content": clarification_content,
                "domain_result": None,  # 不设置领域结果
                "category_result": None,  # 不设置类别结果
                "focus_points_status": None,  # 不进入关注点流程
                "requires_domain_clarification": True  # 标记需要领域澄清
            }

        except Exception as e:
            self.logger.error(f"处理领域分类失败澄清时出错: {e}", exc_info=True)
            # 回退到通用引导
            content = await self.handle_start_requirement_gathering(message, session_id, {})
            return {
                "content": content,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None
            }

    async def _generate_domain_clarification_question(self, user_message: str) -> str:
        """
        使用专业模板生成领域澄清问题

        Args:
            user_message: 用户原始消息

        Returns:
            str: 澄清问题内容
        """
        try:
            # 使用专业的澄清问题模板
            return await self._generate_clarification_using_template(
                focus_point_name="业务领域识别",
                focus_point_description="确定用户需求所属的专业领域，以便提供针对性的服务",
                user_answer=user_message,
                conversation_history="用户表达了业务需求，但领域分类不够明确",
                completeness=0.3  # 严重不足型，需要重新引导
            )

        except Exception as e:
            self.logger.error(f"生成领域澄清问题失败: {e}")
            # 回退到简化版本
            return self._generate_fallback_domain_clarification(user_message)

    async def _generate_clarification_using_template(self, focus_point_name: str, focus_point_description: str,
                                                   user_answer: str, conversation_history: str,
                                                   completeness: float) -> str:
        """
        使用clarification_question.md模板生成澄清问题

        Args:
            focus_point_name: 关注点名称
            focus_point_description: 关注点描述
            user_answer: 用户回答
            conversation_history: 对话历史
            completeness: 信息完整度 (0.0-1.0)

        Returns:
            str: 生成的澄清问题
        """
        try:
            # 读取模板文件
            template_path = "backend/prompts/clarification_question.md"
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()

            # 构建提示词
            prompt = template_content.format(
                focus_point_name=focus_point_name,
                focus_point_description=focus_point_description,
                conversation_history=conversation_history,
                user_answer=user_answer,
                completeness=completeness
            )

            # 调用LLM生成澄清问题
            if hasattr(self, 'llm_client') and self.llm_client:
                try:
                    # 使用标准的LLM调用方式
                    response = await self.llm_client.call_llm(
                        messages=[{"role": "user", "content": prompt}],
                        agent_name="domain_clarification_generator",
                        temperature=0.7,
                        max_tokens=300
                    )

                    if response and response.get('content'):
                        clarification = response['content'].strip()
                        self.logger.info(f"使用模板生成澄清问题成功: {clarification[:100]}...")
                        return clarification

                except Exception as e:
                    self.logger.error(f"LLM调用失败: {e}")
                    # 继续到回退逻辑

            # LLM不可用时的回退
            return self._generate_fallback_domain_clarification(user_answer)

        except Exception as e:
            self.logger.error(f"使用模板生成澄清问题失败: {e}")
            return self._generate_fallback_domain_clarification(user_answer)

    def _generate_fallback_domain_clarification(self, user_message: str) -> str:
        """
        生成回退版本的领域澄清问题

        Args:
            user_message: 用户消息

        Returns:
            str: 回退版本的澄清问题
        """
        message_lower = user_message.lower()

        # 根据用户输入选择合适的回退策略
        if any(word in message_lower for word in ["项目", "做个", "开发", "设计", "制作"]):
            return """我理解您想要做一个项目，为了更好地帮助您，请告诉我这是关于：

🎨 **设计类项目**：Logo设计、海报制作、品牌视觉、UI设计等
💻 **软件开发**：网站开发、APP制作、小程序、系统开发等
📢 **营销推广**：活动策划、广告投放、品牌推广、内容营销等
⚖️ **法律咨询**：合同审查、知识产权、法律风险评估等

请选择最接近的类型，或者用您自己的话描述一下具体想做什么？"""

        elif any(word in message_lower for word in ["帮我", "需要", "想要", "希望"]):
            return """我很乐意帮助您！为了提供最合适的建议，请告诉我：

**您的具体目标是什么？**
• 是想解决某个问题？
• 还是要创建/制作某个产品？
• 或者需要专业建议和咨询？

**涉及哪个专业领域？**
• 设计创意（Logo、海报、品牌等）
• 技术开发（网站、APP、系统等）
• 营销推广（活动、广告、品牌等）
• 法律咨询（合同、知识产权等）

请用您自己的话简单描述一下，我会根据您的回答提供针对性的帮助。"""

        else:
            return """为了更好地为您服务，我需要了解一下您的具体需求：

**请告诉我您希望在以下哪个方面得到帮助：**

🎨 **创意设计** - Logo设计、海报制作、品牌视觉、UI界面设计
💻 **技术开发** - 网站建设、手机APP、小程序、软件系统
📢 **营销推广** - 活动策划、广告投放、品牌推广、市场营销
⚖️ **法律服务** - 合同起草、知识产权、法律咨询、风险评估

您可以直接选择类型，也可以用自己的话描述具体需求，我会为您提供专业的需求分析和建议。"""

    async def _save_clarification_state(self, session_id: str, user_id: str, clarification_type: str, original_message: str) -> bool:
        """
        保存澄清状态到数据库

        Args:
            session_id: 会话ID
            user_id: 用户ID
            clarification_type: 澄清类型（如 domain_clarification）
            original_message: 用户原始消息

        Returns:
            bool: 保存是否成功
        """
        try:
            # 根据澄清类型确定状态值
            status_mapping = {
                "domain_clarification": "DOMAIN_CLARIFICATION",
                "category_clarification": "CATEGORY_CLARIFICATION",
                "direct_domain_selection": "DIRECT_SELECTION"
            }

            status_value = status_mapping.get(clarification_type, "DOMAIN_CLARIFICATION")

            # 构建metadata
            clarification_data = {
                "clarification_type": clarification_type,
                "original_message": original_message,
                "timestamp": time.time(),
                "status": "waiting_for_clarification",
                "attempt_count": 1,  # 澄清尝试次数
                "max_attempts": 3    # 最大澄清次数
            }

            # 更新conversations表的status和metadata字段
            await self.db_manager.execute_update(
                """
                UPDATE conversations
                SET status = ?,
                    metadata = ?,
                    updated_at = datetime('now')
                WHERE conversation_id = ? AND user_id = ?
                """,
                (status_value, json.dumps(clarification_data), session_id, user_id)
            )

            self.logger.info(f"澄清状态已保存到数据库 - session: {session_id}, status: {status_value}")
            return True

        except Exception as e:
            self.logger.error(f"保存澄清状态失败: {e}")
            return False

    async def _handle_clarification_response(self, message: str, session_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        处理用户对澄清问题的回答

        Args:
            message: 用户回答
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Optional[Dict[str, Any]]: 处理结果，None表示不是澄清回答
        """
        try:
            # 1. 检查是否有澄清状态
            clarification_state = await self._get_clarification_state(session_id, user_id)
            if not clarification_state:
                return None  # 不是澄清回答，正常处理

            clarification_type = clarification_state.get("type", "")

            # 2. 检查是否是直接选择回答
            if clarification_type == "direct_domain_selection":
                return await self._handle_direct_selection_response(message, session_id, user_id, clarification_state)

            # 3. 检查是否是领域澄清回答
            if clarification_type != "domain_clarification":
                return None  # 不是领域澄清回答，正常处理

            self.logger.info(f"检测到澄清回答 - session: {session_id}, 原始消息: {clarification_state.get('original_message', '')[:50]}...")

            # 2. 基于用户回答进行增强的领域分类
            original_message = clarification_state.get('original_message', '')
            enhanced_message = f"{original_message} {message}".strip()

            self.logger.info(f"使用增强消息进行重新分类: {enhanced_message[:100]}...")

            # 3. 重新进行领域分类
            domain_result = await self._perform_domain_classification(enhanced_message, session_id, user_id)
            self.logger.info(f"澄清后领域分类结果: {domain_result}")

            # 4. 如果仍然失败，检查澄清次数
            if not domain_result or domain_result.get("domain_id") == "LY_100":
                attempt_count = clarification_state.get("attempt_count", 1)
                max_attempts = clarification_state.get("max_attempts", 3)

                self.logger.info(f"澄清后仍然分类失败 - session: {session_id}, 尝试次数: {attempt_count}/{max_attempts}")

                if attempt_count < max_attempts:
                    # 继续澄清，增加尝试次数
                    return await self._continue_clarification(message, session_id, user_id, clarification_state, attempt_count + 1)
                else:
                    # 达到最大次数，提供直接选择
                    self.logger.info(f"达到最大澄清次数，提供直接选择 - session: {session_id}")
                    return await self._provide_direct_domain_selection(message, session_id, user_id)

            # 5. 分类成功，清除澄清状态
            await self._clear_clarification_state(session_id, user_id)

            # 6. 继续类别分类流程
            category_result = None
            if domain_result.get("status") == "completed":
                category_result = await self._perform_category_classification(enhanced_message, domain_result, session_id, user_id)
                self.logger.info(f"澄清后类别分类结果: {category_result}")

            # 7. 继续正常的需求采集流程
            focus_points_status = None
            content = None

            if category_result and category_result.get("category_id"):
                # 获取关注点并初始化
                focus_points = await self._get_focus_points_for_category(domain_result.get("domain_id"), category_result.get("category_id"))
                if focus_points:
                    # 初始化关注点状态
                    await self.initialize_focus_points(session_id, user_id, focus_points)

                    # 生成第一个问题
                    content = await self._generate_first_collection_question(enhanced_message, domain_result, category_result, focus_points, session_id, user_id, "neutral")
                    focus_points_status = {"initialized": True, "total_points": len(focus_points)}

                    # 保存领域、类别信息并更新状态
                    await self._save_domain_category_and_update_state(session_id, user_id, domain_result, category_result, "COLLECTING_INFO")
                    self.logger.info(f"澄清成功，状态已更新为COLLECTING_INFO - session: {session_id}")
                else:
                    content = f"感谢您的澄清！我已识别这是关于{domain_result.get('domain_name', '未知领域')}的{category_result.get('category_name', '需求')}。让我来帮您详细梳理相关需求。\n\n请详细描述一下您的具体需求和期望目标。"
            else:
                # 类别分类失败，但领域分类成功
                content = f"感谢您的澄清！我已识别这是关于{domain_result.get('domain_name', '未知领域')}的需求。请详细描述一下您的具体需求，我会为您提供专业的建议。"

            return {
                "content": content,
                "domain_result": domain_result,
                "category_result": category_result,
                "focus_points_status": focus_points_status,
                "clarification_resolved": True  # 标记澄清已解决
            }

        except Exception as e:
            self.logger.error(f"处理澄清回答失败: {e}", exc_info=True)
            # 清除澄清状态，避免卡住
            await self._clear_clarification_state(session_id, user_id)
            return None  # 回退到正常处理流程

    async def _get_clarification_state(self, session_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        从数据库获取澄清状态

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Optional[Dict[str, Any]]: 澄清状态，None表示没有澄清状态
        """
        try:
            # 从数据库查询澄清状态
            result = await self.db_manager.get_record(
                """
                SELECT status, metadata
                FROM conversations
                WHERE conversation_id = ? AND user_id = ?
                """,
                (session_id, user_id)
            )

            if not result:
                self.logger.debug(f"会话不存在 - session: {session_id}")
                return None

            status = result.get('status', '')
            metadata_str = result.get('metadata', '{}')

            # 检查是否是澄清状态
            clarification_statuses = ["DOMAIN_CLARIFICATION", "CATEGORY_CLARIFICATION", "DIRECT_SELECTION"]
            if status not in clarification_statuses:
                self.logger.debug(f"会话不在澄清状态 - session: {session_id}, status: {status}")
                return None

            # 解析metadata
            try:
                metadata = json.loads(metadata_str) if metadata_str else {}
            except json.JSONDecodeError:
                self.logger.warning(f"metadata解析失败 - session: {session_id}")
                metadata = {}

            # 构建澄清状态数据
            clarification_state = {
                "type": metadata.get("clarification_type", "domain_clarification"),
                "original_message": metadata.get("original_message", ""),
                "timestamp": metadata.get("timestamp", time.time()),
                "status": metadata.get("status", "waiting_for_clarification"),
                "db_status": status
            }

            self.logger.debug(f"获取澄清状态成功 - session: {session_id}, type: {clarification_state['type']}")
            return clarification_state

        except Exception as e:
            self.logger.error(f"获取澄清状态失败: {e}")
            return None

    async def _clear_clarification_state(self, session_id: str, user_id: str) -> bool:
        """
        清除澄清状态，恢复到正常状态

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            bool: 清除是否成功
        """
        try:
            # 将状态恢复为active，清空metadata
            await self.db_manager.execute_update(
                """
                UPDATE conversations
                SET status = 'active',
                    metadata = '{}',
                    updated_at = datetime('now')
                WHERE conversation_id = ? AND user_id = ?
                """,
                (session_id, user_id)
            )

            self.logger.info(f"澄清状态已清除 - session: {session_id}")
            return True

        except Exception as e:
            self.logger.error(f"清除澄清状态失败: {e}")
            return False

    async def _continue_clarification(self, message: str, session_id: str, user_id: str,
                                    previous_state: Dict[str, Any], attempt_count: int) -> Dict[str, Any]:
        """
        继续澄清流程，处理多轮澄清

        Args:
            message: 用户当前回答
            session_id: 会话ID
            user_id: 用户ID
            previous_state: 之前的澄清状态
            attempt_count: 当前尝试次数

        Returns:
            Dict[str, Any]: 继续澄清的响应
        """
        try:
            self.logger.info(f"继续澄清流程 - session: {session_id}, 尝试次数: {attempt_count}")

            # 构建更详细的澄清历史
            original_message = previous_state.get("original_message", "")
            clarification_history = f"原始需求: {original_message}\n用户澄清: {message}"

            # 生成更具体的澄清问题
            clarification_content = await self._generate_enhanced_clarification_question(
                original_message, message, attempt_count
            )

            # 更新澄清状态，增加尝试次数
            clarification_data = {
                "clarification_type": previous_state.get("clarification_type", "domain_clarification"),
                "original_message": original_message,
                "clarification_history": clarification_history,
                "timestamp": time.time(),
                "status": "waiting_for_clarification",
                "attempt_count": attempt_count,
                "max_attempts": previous_state.get("max_attempts", 3)
            }

            # 保存更新的澄清状态
            await self.db_manager.execute_update(
                """
                UPDATE conversations
                SET metadata = ?,
                    updated_at = datetime('now')
                WHERE conversation_id = ? AND user_id = ?
                """,
                (json.dumps(clarification_data), session_id, user_id)
            )

            return {
                "content": clarification_content,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None,
                "requires_domain_clarification": True,
                "attempt_count": attempt_count
            }

        except Exception as e:
            self.logger.error(f"继续澄清失败: {e}")
            # 回退到直接选择
            return await self._provide_direct_domain_selection(message, session_id, user_id)

    async def _generate_enhanced_clarification_question(self, original_message: str,
                                                      clarification_answer: str,
                                                      attempt_count: int) -> str:
        """
        生成增强的澄清问题，基于多轮对话历史

        Args:
            original_message: 用户原始消息
            clarification_answer: 用户澄清回答
            attempt_count: 当前尝试次数

        Returns:
            str: 增强的澄清问题
        """
        try:
            # 根据尝试次数调整澄清策略
            if attempt_count == 2:
                # 第二次澄清：更直接，提供具体例子
                return f"""我理解您之前提到"{original_message}"，刚才您回答了"{clarification_answer}"。

为了确保为您提供最准确的服务，让我更直接地询问：

**请从以下选项中选择最接近的一项：**

1️⃣ **设计创意** - 比如：公司Logo、产品海报、品牌形象、UI界面设计
2️⃣ **技术开发** - 比如：企业网站、手机APP、小程序、管理系统
3️⃣ **营销推广** - 比如：活动策划、广告投放、社交媒体运营、品牌推广
4️⃣ **法律服务** - 比如：合同起草、知识产权申请、法律风险评估

**请直接回复数字（如"1"）或说出具体类型（如"设计创意"）**"""

            elif attempt_count >= 3:
                # 第三次及以上：最简化的选择
                return """让我们简化一下，请直接告诉我：

🎨 **设计** - Logo、海报、界面等视觉设计
💻 **开发** - 网站、APP、软件等技术开发
📢 **营销** - 推广、广告、活动等营销服务
⚖️ **法律** - 合同、知识产权等法律服务

**请直接说出您需要的服务类型**"""

            else:
                # 默认情况，使用专业模板
                conversation_history = f"用户原始需求: {original_message}\n用户澄清回答: {clarification_answer}"
                return await self._generate_clarification_using_template(
                    focus_point_name="业务领域识别",
                    focus_point_description="确定用户需求所属的专业领域",
                    user_answer=clarification_answer,
                    conversation_history=conversation_history,
                    completeness=0.2  # 更低的完整度，表示需要更强的引导
                )

        except Exception as e:
            self.logger.error(f"生成增强澄清问题失败: {e}")
            return "请告诉我您具体需要什么类型的服务：设计、开发、营销还是法律？"

    async def _handle_direct_selection_response(self, message: str, session_id: str, user_id: str,
                                              clarification_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理用户的直接选择回答

        Args:
            message: 用户选择回答
            session_id: 会话ID
            user_id: 用户ID
            clarification_state: 澄清状态

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.logger.info(f"处理直接选择回答 - session: {session_id}, 选择: {message}")

            # 解析用户选择
            domain_mapping = self._parse_user_selection(message)

            if not domain_mapping:
                # 选择无效，提供更简化的选项
                return await self._provide_simplified_selection(message, session_id, user_id)

            domain_id = domain_mapping["domain_id"]
            domain_name = domain_mapping["domain_name"]

            self.logger.info(f"用户选择解析成功 - domain_id: {domain_id}, domain_name: {domain_name}")

            # 清除澄清状态
            await self._clear_clarification_state(session_id, user_id)

            # 构建领域分类结果
            domain_result = {
                "domain_id": domain_id,
                "domain_name": domain_name,
                "confidence": 0.9,  # 用户直接选择，置信度高
                "status": "completed",
                "reasoning": f"用户直接选择: {message}"
            }

            # 进行类别分类
            original_message = clarification_state.get("original_message", "")
            enhanced_message = f"{original_message} {message}".strip()

            category_result = await self._perform_category_classification(enhanced_message, domain_result, session_id, user_id)
            self.logger.info(f"直接选择后类别分类结果: {category_result}")

            # 继续正常的需求采集流程
            focus_points_status = None
            content = None

            if category_result and category_result.get("category_id"):
                # 获取关注点并初始化
                focus_points = await self._get_focus_points_for_category(domain_id, category_result.get("category_id"))
                if focus_points:
                    # 初始化关注点状态
                    await self.initialize_focus_points(session_id, user_id, focus_points)

                    # 生成第一个问题
                    content = await self._generate_first_collection_question(enhanced_message, domain_result, category_result, focus_points, session_id, user_id, "neutral")
                    focus_points_status = {"initialized": True, "total_points": len(focus_points)}

                    # 保存领域、类别信息并更新状态
                    await self._save_domain_category_and_update_state(session_id, user_id, domain_result, category_result, "COLLECTING_INFO")
                    self.logger.info(f"直接选择成功，状态已更新为COLLECTING_INFO - session: {session_id}")
                else:
                    content = f"感谢您的选择！我已确认这是关于{domain_name}的{category_result.get('category_name', '需求')}。让我来帮您详细梳理相关需求。\n\n请详细描述一下您的具体需求和期望目标。"
            else:
                # 类别分类失败，但领域分类成功
                content = f"感谢您的选择！我已确认这是关于{domain_name}的需求。请详细描述一下您的具体需求，我会为您提供专业的建议。"

            return {
                "content": content,
                "domain_result": domain_result,
                "category_result": category_result,
                "focus_points_status": focus_points_status,
                "direct_selection_resolved": True  # 标记直接选择已解决
            }

        except Exception as e:
            self.logger.error(f"处理直接选择回答失败: {e}")
            # 清除澄清状态，避免卡住
            await self._clear_clarification_state(session_id, user_id)
            return None  # 回退到正常处理流程

    def _parse_user_selection(self, message: str) -> Optional[Dict[str, str]]:
        """
        解析用户的选择回答

        Args:
            message: 用户消息

        Returns:
            Optional[Dict[str, str]]: 解析结果，包含domain_id和domain_name
        """
        message_lower = message.lower().strip()

        # 数字选择映射
        number_mapping = {
            "1": {"domain_id": "LY_001", "domain_name": "平面设计"},
            "2": {"domain_id": "LY_002", "domain_name": "UI/UX设计"},
            "3": {"domain_id": "LY_003", "domain_name": "营销推广"},
            "4": {"domain_id": "LY_004", "domain_name": "法律咨询"},
            "5": {"domain_id": "LY_005", "domain_name": "软件开发"},
            "一": {"domain_id": "LY_001", "domain_name": "平面设计"},
            "二": {"domain_id": "LY_002", "domain_name": "UI/UX设计"},
            "三": {"domain_id": "LY_003", "domain_name": "营销推广"},
            "四": {"domain_id": "LY_004", "domain_name": "法律咨询"},
            "五": {"domain_id": "LY_005", "domain_name": "软件开发"}
        }

        # 关键词映射
        keyword_mapping = {
            "设计": {"domain_id": "LY_001", "domain_name": "平面设计"},
            "平面设计": {"domain_id": "LY_001", "domain_name": "平面设计"},
            "logo": {"domain_id": "LY_001", "domain_name": "平面设计"},
            "海报": {"domain_id": "LY_001", "domain_name": "平面设计"},
            "ui": {"domain_id": "LY_002", "domain_name": "UI/UX设计"},
            "ux": {"domain_id": "LY_002", "domain_name": "UI/UX设计"},
            "界面": {"domain_id": "LY_002", "domain_name": "UI/UX设计"},
            "交互": {"domain_id": "LY_002", "domain_name": "UI/UX设计"},
            "营销": {"domain_id": "LY_003", "domain_name": "营销推广"},
            "推广": {"domain_id": "LY_003", "domain_name": "营销推广"},
            "广告": {"domain_id": "LY_003", "domain_name": "营销推广"},
            "活动": {"domain_id": "LY_003", "domain_name": "营销推广"},
            "法律": {"domain_id": "LY_004", "domain_name": "法律咨询"},
            "合同": {"domain_id": "LY_004", "domain_name": "法律咨询"},
            "知识产权": {"domain_id": "LY_004", "domain_name": "法律咨询"},
            "开发": {"domain_id": "LY_005", "domain_name": "软件开发"},
            "软件": {"domain_id": "LY_005", "domain_name": "软件开发"},
            "网站": {"domain_id": "LY_005", "domain_name": "软件开发"},
            "app": {"domain_id": "LY_005", "domain_name": "软件开发"},
            "小程序": {"domain_id": "LY_005", "domain_name": "软件开发"}
        }

        # 首先检查数字选择
        for key, mapping in number_mapping.items():
            if key in message_lower:
                return mapping

        # 然后检查关键词
        for keyword, mapping in keyword_mapping.items():
            if keyword in message_lower:
                return mapping

        return None

    async def _provide_simplified_selection(self, message: str, session_id: str, user_id: str) -> Dict[str, Any]:
        """
        提供最简化的选择机制

        Args:
            message: 用户无效选择
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 简化选择的响应
        """
        try:
            self.logger.info(f"提供简化选择 - session: {session_id}")

            content = f"""我没有完全理解您的选择"{message}"。

让我们用最简单的方式：

**请直接回复以下4个字中的任意一个：**

• **设计** - 视觉设计相关
• **开发** - 技术开发相关
• **营销** - 推广营销相关
• **法律** - 法律服务相关

或者您可以说"我不确定"，我会为您提供通用的需求收集服务。"""

            return {
                "content": content,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None,
                "requires_direct_selection": True,
                "simplified_mode": True
            }

        except Exception as e:
            self.logger.error(f"提供简化选择失败: {e}")
            # 最终兜底：清除澄清状态，进入通用流程
            await self._clear_clarification_state(session_id, user_id)
            content = "让我直接帮您收集需求信息。请详细描述一下您的项目需求和期望目标。"
            return {
                "content": content,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None,
                "fallback_to_general": True
            }

    async def _provide_direct_domain_selection(self, message: str, session_id: str, user_id: str) -> Dict[str, Any]:
        """
        提供直接的领域选择机制

        Args:
            message: 用户消息
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 直接选择的响应
        """
        try:
            self.logger.info(f"提供直接领域选择 - session: {session_id}")

            content = """我理解您的需求可能比较特殊或复杂。为了确保为您提供最合适的服务，请直接从以下选项中选择最接近的领域：

**请回复对应的数字或直接说出领域名称：**

1️⃣ **平面设计** - Logo设计、海报制作、品牌视觉、宣传材料等
2️⃣ **UI/UX设计** - 界面设计、用户体验、交互设计、原型制作等
3️⃣ **营销推广** - 活动策划、广告投放、品牌推广、市场营销等
4️⃣ **法律咨询** - 合同审查、知识产权、法律风险评估、诉讼咨询等
5️⃣ **软件开发** - 网站建设、APP开发、小程序、系统开发等

**或者您可以：**
• 详细描述您的具体需求，我会重新为您分析
• 说明您的项目目标和期望效果

请选择最合适的选项，我会立即为您提供专业的需求分析服务。"""

            # 保存直接选择状态
            await self._save_clarification_state(session_id, user_id, "direct_domain_selection", message)

            return {
                "content": content,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None,
                "requires_direct_selection": True  # 标记需要直接选择
            }

        except Exception as e:
            self.logger.error(f"提供直接领域选择失败: {e}")
            # 回退到通用引导
            content = "让我直接帮您收集需求信息。请详细描述一下您的项目需求和期望目标。"
            return {
                "content": content,
                "domain_result": None,
                "category_result": None,
                "focus_points_status": None
            }

    async def handle_process_answer_and_ask_next(self, message: str, session_id: str, user_id: str, history: Any, decision_result: Dict[str, Any]) -> str:
        """
        核心业务逻辑 - 处理用户回答并决定下一步

        这是整个需求采集系统的心脏，负责：
        1. 识别当前处理的关注点
        2. 信息提取和完整度评估
        3. 决策：追问 vs 下一个问题
        4. 状态更新
        """
        self.logger.info("正在处理用户回答...")

        # 创建进度指示器
        progress = ProgressIndicator(session_id, user_id)
        progress.update_progress(ProgressStage.STARTING, 10, "🔍 正在分析您的回答...")

        try:
            # 提取用户情绪信息
            user_emotion = decision_result.get("emotion", "neutral")
            self.logger.info(f"用户当前情绪: {user_emotion}")

            # 1. 获取会话上下文和关注点定义
            progress.update_progress(ProgressStage.ANALYZING, 20, "📋 正在加载会话上下文...")
            session_context = await self.session_context_manager.load_session_context(session_id, user_id)
            if not session_context.current_focus_points_definitions:
                # 恢复领域分类信息
                await self._restore_domain_category_info(session_id, user_id)
                session_context = await self.session_context_manager.load_session_context(session_id, user_id)

            # 2. 确定当前正在处理的关注点
            progress.update_progress(ProgressStage.PROCESSING, 35, "🎯 正在识别当前关注点...")
            processing_point_id = await self.state_manager.get_processing_point(session_id, user_id)

            # 🔥 添加详细调试信息
            if not processing_point_id:
                # 获取所有关注点状态进行调试
                all_statuses = await self.state_manager.focus_point_manager.load_focus_points_status(session_id, user_id)
                self.logger.warning(f"请求处理回答，但没有找到正在处理的关注点")
                self.logger.info(f"[DEBUG] 当前所有关注点状态: {all_statuses}")

                # 检查是否有processing状态的关注点
                processing_points = [point_id for point_id, status_info in all_statuses.items()
                                   if status_info.get("status") == "processing"]
                if processing_points:
                    self.logger.error(f"[ERROR] 数据库中存在processing状态的关注点，但get_processing_point未找到: {processing_points}")

                await self.state_manager.clear_all_processing_status(session_id, user_id)
                return await self.generate_next_question(session_id, user_id, session_context.current_focus_points_definitions, message, user_emotion)

            # 3. 获取关注点定义
            point_definition = next((p for p in session_context.current_focus_points_definitions if p.get("id") == processing_point_id), None)
            if not point_definition:
                self.logger.error(f"无法找到ID为 {processing_point_id} 的关注点定义")
                return await self.generate_next_question(session_id, user_id, session_context.current_focus_points_definitions, message, user_emotion)

            # 4. 🔥 调用信息提取器进行核心处理
            progress.update_progress(ProgressStage.EXTRACTING, 50, "🔍 正在提取关键信息...")
            conversation_history = await self._get_recent_conversation_history(session_id, user_id)
            extraction_result = await self.information_extractor_agent.extract_values(
                user_input=message,
                focus_points=[point_definition],
                conversation_history=conversation_history
            )

            # 5. 解析提取结果
            progress.update_progress(ProgressStage.PROCESSING, 70, "📊 正在分析信息完整度...")
            new_summary_json = extraction_result.get("updated_summary_json", '{"extracted_points": []}')
            await self.summary_manager.update_summary(session_id, user_id, new_summary_json)

            summary_data = json.loads(new_summary_json)
            latest_points_status = summary_data.get("extracted_points", [])
            updated_point_info = next((p for p in latest_points_status if p.get("name") == point_definition.get("name")), None)

            # 6. 基于完整度进行决策
            if updated_point_info:
                completeness = updated_point_info.get("completeness", 0.0)
                extracted_value = updated_point_info.get("value", "")

                # 确保extracted_value是字符串类型
                if isinstance(extracted_value, dict):
                    extracted_value = json.dumps(extracted_value, ensure_ascii=False)
                elif not isinstance(extracted_value, str):
                    extracted_value = str(extracted_value)

                # 更新关注点状态 - 🔥 修复：正确的状态转换逻辑
                threshold = ConversationConstants.get_completeness_threshold()  # 0.8
                if completeness >= threshold:
                    # 完整度足够，标记为完成
                    status = "completed"
                else:
                    # 完整度不够，保持processing状态以便继续追问
                    status = "processing"

                await self.state_manager.update_focus_point_status(
                    session_id=session_id,
                    user_id=user_id,
                    point_id=processing_point_id,
                    status=status,
                    value=extracted_value
                )

                if status == "completed":
                    # 信息完整，生成下一个问题
                    progress.update_progress(ProgressStage.GENERATING, 90, "✨ 正在准备下一个问题...")
                    self.logger.info(f"关注点 '{point_definition['name']}' 已完成，询问下一个问题")
                    result = await self.generate_next_question(session_id, user_id, session_context.current_focus_points_definitions, message, user_emotion)
                    progress.update_progress(ProgressStage.COMPLETED, 100, "✅ 处理完成")
                    return result
                else:
                    # 信息不完整，生成追问
                    progress.update_progress(ProgressStage.GENERATING, 85, "🤔 正在生成追问...")
                    self.logger.info(f"关注点 '{point_definition['name']}' 信息不完整 (完整度: {completeness})，生成追问")
                    result = await self._generate_clarification_question(point_definition, message, completeness, session_id, user_id)
                    progress.update_progress(ProgressStage.COMPLETED, 100, "✅ 追问生成完成")
                    return result
            else:
                # 未能提取到信息，生成追问
                self.logger.warning(f"未能从用户回答中提取到关于 '{point_definition.get('name')}' 的信息")
                return await self._generate_clarification_question(point_definition, message, 0.0, session_id, user_id)

        except Exception as e:
            self.logger.error(f"处理用户回答失败: {e}", exc_info=True)
            return "抱歉，我在处理您的回答时遇到了问题。请重新描述一下您的需求。"

    async def _get_recent_conversation_history(self, session_id: str, user_id: str) -> str:
        """获取最近的对话历史"""
        try:
            from backend.services.conversation_history_service import HistoryConfig, HistoryFormat
            history_config = HistoryConfig(
                max_turns=5,  # 减少历史记录数量，避免混乱
                format_type=HistoryFormat.SIMPLE
            )

            # 🔥 临时修复：如果使用global_shared_agent，返回空历史避免混乱
            if session_id == "global_shared_agent" or user_id == "global_shared_agent":
                self.logger.info(f"[对话历史] 检测到全局共享Agent，返回空历史避免数据混乱")
                return "这是新的对话开始"

            history = await self.history_service.get_conversation_history(
                session_id, user_id, history_config
            )

            # 额外的安全检查：如果历史记录包含明显不相关的内容，返回空历史
            if self._contains_irrelevant_history(history):
                self.logger.warning(f"[对话历史] 检测到不相关的历史记录，返回空历史")
                return "这是新的对话开始"

            return history
        except Exception as e:
            self.logger.warning(f"获取对话历史失败: {e}")
            return "这是新的对话开始"

    def _contains_irrelevant_history(self, history: str) -> bool:
        """检查历史记录是否包含不相关的内容"""
        if not history or history == "这是新的对话开始":
            return False

        # 检查是否包含过多的重复问候语
        greeting_count = history.count("你好") + history.count("您好")
        if greeting_count > 3:
            return True

        # 检查是否包含明显的错误信息
        error_indicators = [
            "处理失败",
            "开始需求采集失败",
            "object has no attribute",
            "我可能没有完全理解您的意思"
        ]

        for indicator in error_indicators:
            if indicator in history:
                return True

        return False

    async def generate_next_question(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]], user_message: str = "", emotion: str = "neutral") -> str:
        """生成下一个问题"""
        try:
            if not focus_points:
                return self.unified_config_loader.get_message_template("user_interaction.defaults.requirement_prompt")

            # 检查是否所有必需的关注点都已覆盖
            all_required_covered = await self.state_manager.check_all_required_covered(session_id, user_id, focus_points)
            if all_required_covered:
                # 所有关注点完成，生成文档
                document_content = await self._generate_document(session_id, user_id)
                return self.unified_config_loader.get_message_template("system.document.confirmation_prefix") + document_content

            # 🔥 修复：确保数据库状态同步 - 添加小延迟让数据库事务完全提交
            import asyncio
            await asyncio.sleep(0.01)  # 10ms延迟确保数据库写入完成

            # 获取下一个待处理的关注点
            selected_point = await self.state_manager.get_next_pending_point(session_id, user_id, focus_points)
            self.logger.info(f"[DEBUG] generate_next_question - 选择的关注点: {selected_point.get('id') if selected_point else None} ({selected_point.get('name') if selected_point else 'None'})")
            if selected_point:
                point_id = selected_point["id"]

                # 使用智能问题生成替代简单模板
                if self.integrated_reply_system and self.integrated_reply_system.reply_factory:
                    try:
                        # 获取项目类型和进度信息
                        session_context = await self.session_context_manager.load_session_context(session_id, user_id)

                        # 优先从关注点定义中获取项目类型信息
                        project_type = ""
                        if focus_points and len(focus_points) > 0:
                            # 从第一个关注点获取category_name和domain_name
                            first_point = focus_points[0]
                            category_name = first_point.get('category_name', '')
                            domain_name = first_point.get('domain_name', '')
                            if category_name and domain_name:
                                project_type = f"{domain_name}-{category_name}"
                            elif category_name:
                                project_type = category_name
                            elif domain_name:
                                project_type = domain_name

                        # 如果从关注点获取不到，尝试从session_context获取
                        if not project_type:
                            project_type = session_context.current_category or session_context.current_domain or ""

                        progress_info = await self._get_progress_info(session_id, user_id, focus_points)
                        self.logger.info(f"🎯 [进度信息] 生成的进度信息: '{progress_info}'")

                        # 获取对话历史用于智能问题生成
                        conversation_history = await self._get_recent_conversation_history(session_id, user_id)
                        user_input_for_generation = user_message or self.unified_config_loader.get_message_template("business.question.user_context")

                        # 🚀 使用优化的一步式问题生成（合并生成+优化）
                        self.logger.info(f"✅ [第一次收集] 使用优化问题生成 - 关注点: {selected_point['name']}, 用户情绪: {emotion}")
                        base_question = await self.integrated_reply_system.reply_factory.generate_optimized_question(
                            name=selected_point['name'],
                            description=selected_point['description'],
                            required=selected_point.get('required', False),
                            examples=selected_point.get('example', ''),
                            project_type=project_type,
                            progress_info=progress_info,
                            user_input=user_input_for_generation,
                            session_id=session_id,
                            user_id=user_id,
                            emotion=emotion,  # 传递情绪信息
                            conversation_history=conversation_history
                        )

                        self.logger.info(f"✅ [第一次收集] 优化问题生成成功，长度: {len(base_question) if base_question else 0}")

                        # 验证优化问题生成的结果
                        if not base_question or len(base_question.strip()) == 0:
                            self.logger.warning("优化问题生成返回空结果，使用回退方案")
                            raise ValueError("优化问题生成返回空结果")

                    except Exception as e:
                        self.logger.warning(f"智能问题生成失败，使用回退方案: {e}")
                        # 回退到硬编码的安全模板（因为配置模板已废弃）
                        base_question = f"关于「{selected_point['name']}」，{selected_point['description']}"
                        if 'example' in selected_point and selected_point['example']:
                            base_question += f"\n\n💡 **参考示例**：{selected_point['example']}"
                else:
                    # 如果没有集成回复系统，使用简单模板作为回退
                    self.logger.warning("智能问题生成条件不满足，使用硬编码回退方案")
                    base_question = f"关于「{selected_point['name']}」，{selected_point['description']}"
                    if 'example' in selected_point and selected_point['example']:
                        base_question += f"\n\n💡 **参考示例**：{selected_point['example']}"

                # 设置关注点为正在处理状态
                await self.state_manager.set_point_processing_safely(session_id, user_id, point_id)
                return base_question

            return self.unified_config_loader.get_message_template("user_interaction.defaults.detailed_requirement")

        except Exception as e:
            self.logger.error(f"生成下一个问题失败: {e}", exc_info=True)
            return self.unified_config_loader.get_message_template("user_interaction.defaults.detailed_requirement")

    async def _get_progress_info(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> str:
        """获取收集进度信息 - 增强版本，提供更详细的进度提示"""
        try:
            if not focus_points:
                return ""

            # 获取关注点优先级配置
            config = self.unified_config_loader
            p0_required = config.get_business_rule("business_rules.focus_point_priority.p0", True)
            p1_required = config.get_business_rule("business_rules.focus_point_priority.p1", True)
            p2_required = config.get_business_rule("business_rules.focus_point_priority.p2", False)

            # 判断是否需要采集该关注点
            def is_collection_required(focus_point):
                priority = focus_point.get("priority", "p0").lower()
                if priority == "p0":
                    return p0_required
                elif priority == "p1":
                    return p1_required
                elif priority == "p2":
                    return p2_required
                return True  # 默认情况下必须采集

            # 只统计需要采集的关注点
            required_points = [point for point in focus_points if is_collection_required(point)]
            total_points = len(required_points)

            if total_points == 0:
                return ""

            completed_count = 0
            processing_count = 0
            pending_count = 0

            # 从数据库获取最新的关注点状态
            try:
                query = self.unified_config_loader.get_database_query("focus_points.get_status")
                status_records = await self.db_manager.execute_query(query, (session_id, user_id))

                # 构建状态字典
                status_dict = {}
                for record in status_records:
                    focus_id = record.get("focus_id")
                    if focus_id:
                        status_dict[focus_id] = record.get("status", "pending")

                # 统计各状态的关注点数量（只统计需要采集的）
                for point in required_points:
                    point_id = point.get('id')
                    if not point_id:
                        continue

                    status = status_dict.get(point_id, 'pending')
                    if status == 'completed':
                        completed_count += 1
                    elif status == 'processing':
                        processing_count += 1
                    else:
                        pending_count += 1

            except Exception as db_error:
                self.logger.warning(f"从数据库获取关注点状态失败: {db_error}")
                # 如果数据库查询失败，使用缓存作为备选
                for point in required_points:
                    try:
                        point_status_info = self.state_manager.get_focus_point_status(point['id'])
                        if point_status_info:
                            status = point_status_info.get('status', 'pending')
                            if status == 'completed':
                                completed_count += 1
                            elif status == 'processing':
                                processing_count += 1
                            else:
                                pending_count += 1
                        else:
                            pending_count += 1
                    except Exception as status_error:
                        self.logger.debug(f"获取关注点 {point['id']} 状态失败: {status_error}")
                        pending_count += 1

            progress_percentage = (completed_count / total_points) * 100

            # 构建详细的进度信息
            progress_parts = []

            # 基础进度信息
            if progress_percentage == 0:
                progress_parts.append("🚀 我们刚开始收集需求信息")
            elif progress_percentage < 25:
                progress_parts.append(f"📝 已收集 {completed_count}/{total_points} 个关注点")
            elif progress_percentage < 50:
                progress_parts.append(f"✨ 很好！已完成 {progress_percentage:.0f}% 的需求收集")
            elif progress_percentage < 75:
                progress_parts.append(f"🎯 太棒了！已完成过半 ({progress_percentage:.0f}%)")
            elif progress_percentage < 100:
                progress_parts.append(f"🏆 非常棒！即将完成 ({progress_percentage:.0f}%)")
            else:
                progress_parts.append("🎉 所有需求信息收集完成")

            # 添加当前处理状态
            if processing_count > 0:
                progress_parts.append(f"正在深入了解 {processing_count} 个方面")

            # 添加剩余任务提示
            if pending_count > 0 and progress_percentage < 100:
                progress_parts.append(f"还有 {pending_count} 个方面待了解")

            return " | ".join(progress_parts)

        except Exception as e:
            self.logger.warning(f"获取进度信息失败: {e}")
            return ""

    async def _generate_clarification_question(self, focus_point_def: Dict[str, Any], user_answer: str, completeness: float, session_id: str, user_id: str) -> str:
        """生成澄清问题"""
        self.logger.info(f"为关注点 '{focus_point_def.get('name')}' 生成追问")

        try:
            # 获取对话历史
            from backend.services.conversation_history_service import HistoryConfig, HistoryFormat
            history_str = await self.history_service.get_conversation_history(
                session_id, user_id, HistoryConfig(format_type=HistoryFormat.SIMPLE)
            )

            # 使用提示词模板生成追问
            prompt = self.prompt_loader.load_prompt(
                "clarification_question",
                {
                    "focus_point_name": focus_point_def.get("name", "当前话题"),
                    "focus_point_description": focus_point_def.get("description", ""),
                    "conversation_history": history_str,
                    "user_answer": user_answer,
                    "completeness": completeness
                }
            )

            if self.llm_client:
                # 通过统一配置服务获取clarification_generator场景的配置
                try:
                    config = self.config_service.get_llm_config_with_metadata("clarification_generator")
                    temperature = config.get("temperature", 0.8)
                except Exception:
                    temperature = 0.8
                response = await self.llm_client.call_llm(
                    messages=[{"role": "user", "content": prompt}],
                    agent_name="clarification_generator",
                    temperature=temperature
                )

                clarification_question = response.get("content", "").strip()
                if clarification_question:
                    return clarification_question

            # 回退到基础追问
            return f"关于「{focus_point_def.get('name')}」，您能提供更详细一些的信息吗？"

        except Exception as e:
            self.logger.error(f"生成澄清问题失败: {e}")
            return f"关于「{focus_point_def.get('name')}」，请您再详细说明一下。"

    async def _generate_document(self, session_id: str, user_id: str) -> str:
        """生成需求确认文档"""
        try:
            # 从会话上下文中获取领域和类别信息
            session_context = await self.session_context_manager.load_session_context(session_id, user_id)
            domain = session_context.current_domain
            category_id = session_context.current_category

            self.logger.info(f"文档生成 - 获取到的领域: {domain}, 类别ID: {category_id}")

            category_name = None
            if self.knowledge_base_agent and category_id:
                categories = await self.knowledge_base_agent.get_categories(domain)
                category_obj = next((c for c in categories if c.get('category_id') == category_id), None)
                if category_obj:
                    category_name = category_obj.get('name')
                    self.logger.info(f"文档生成 - 找到匹配的类别: {category_obj}")
                else:
                    # 尝试字符串转换匹配
                    category_obj = next((c for c in categories if str(c.get('category_id')) == str(category_id)), None)
                    if category_obj:
                        category_name = category_obj.get('name')
                        self.logger.info(f"文档生成 - 通过字符串转换找到匹配的类别: {category_obj}")

            self.logger.info(f"文档生成 - 最终的category_name: {category_name}")
            config = self.unified_config_loader
            project_name = config.get_message_template("system.document.project_name_template", category_name=category_name) if category_name else config.get_message_template("system.document.project_name_default")

            if self.document_generator_agent:
                document_id = await self.document_generator_agent.generate_document(
                    conversation_id=session_id,
                    user_id=user_id,
                    project_name=project_name,
                    category_name=category_name
                )

                if document_id:
                    document_content = await self._get_document_content(document_id, user_id)
                    doc_guidance = self.unified_config_loader.get_message_template("system.document.guidance")
                    return document_content + doc_guidance
                else:
                    return self.unified_config_loader.get_message_template("error.document_generation_failed")
            else:
                return self.unified_config_loader.get_message_template("error.document_generation_not_initialized")

        except Exception as e:
            self.logger.error(f"文档生成失败: {e}", exc_info=True)
            return self.unified_config_loader.get_message_template("error.document_generation_failed")

    async def _get_document_content(self, document_id: str, user_id: str) -> str:
        """从数据库获取文档内容"""
        try:
            config = self.unified_config_loader
            result = await self.db_manager.execute_query(
                config.get_database_query("documents.get_content"),
                (document_id, user_id)
            )
            if result and len(result) > 0:
                return result[0]["content"]
            else:
                return config.get_message_template("error.system")
        except Exception as e:
            self.logger.error(f"获取文档内容失败: {e}")
            return self.unified_config_loader.get_message_template("system.document.content_error")

    async def _restore_domain_category_info(self, session_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        从数据库恢复领域和类别信息（使用 domain_id 和 category_id）
        如果成功恢复，返回包含 domain_id 和 category_id 的字典，否则返回 None。
        """
        self.logger.debug(f"尝试恢复会话 {session_id} 的领域和类别信息")
        try:
            result = await self.db_manager.get_record(
                self.unified_config_loader.get_database_query("conversations.get_domain_category"),
                (session_id, user_id)
            )

            if result and result.get("domain_id") and result.get("category_id"):
                self.logger.info(f"成功恢复领域: {result['domain_id']}, 类别: {result['category_id']}")
                return {"domain_id": result["domain_id"], "category_id": result["category_id"]}
            else:
                self.logger.info(f"会话 {session_id} 没有找到领域和类别信息")
                return None
        except Exception as e:
            self.logger.warning(f"恢复领域和类别信息失败: {e}")
            return None

    async def _save_domain_category_and_update_state(self, session_id: str, user_id: str, domain_result: Dict[str, Any], category_result: Dict[str, Any], new_state: str) -> bool:
        """
        保存领域、类别信息并更新会话状态

        Args:
            session_id: 会话ID
            user_id: 用户ID
            domain_result: 领域分类结果
            category_result: 类别分类结果
            new_state: 新状态名称（如'COLLECTING_INFO'）

        Returns:
            bool: 更新是否成功
        """
        try:
            # 加载当前会话上下文
            session_context = await self.session_context_manager.load_session_context(session_id, user_id)

            # 更新领域和类别信息
            if domain_result and domain_result.get("domain_id"):
                session_context.current_domain = domain_result.get("domain_id")
                session_context.latest_domain_result = domain_result

            if category_result and category_result.get("category_id"):
                session_context.current_category = category_result.get("category_id")
                session_context.latest_category_result = category_result

            # 将字符串状态转换为枚举
            from backend.agents.session_context import ConversationState
            if hasattr(ConversationState, new_state):
                session_context.current_state = getattr(ConversationState, new_state)

                # 保存更新后的会话上下文
                await self.session_context_manager.save_session_context(session_context)

                self.logger.info(f"会话状态已更新: {session_id} -> {new_state}")
                self.logger.info(f"领域信息已保存: {session_context.current_domain}")
                self.logger.info(f"类别信息已保存: {session_context.current_category}")
                return True
            else:
                self.logger.error(f"无效的状态名称: {new_state}")
                return False

        except Exception as e:
            self.logger.error(f"保存领域类别信息并更新会话状态失败: {e}")
            return False

    async def _update_session_state(self, session_id: str, user_id: str, new_state: str) -> bool:
        """
        更新会话状态

        Args:
            session_id: 会话ID
            user_id: 用户ID
            new_state: 新状态名称（如'COLLECTING_INFO'）

        Returns:
            bool: 更新是否成功
        """
        try:
            # 更新SessionContext中的状态
            session_context = await self.session_context_manager.load_session_context(session_id, user_id)

            # 将字符串状态转换为枚举
            from backend.agents.session_context import ConversationState
            if hasattr(ConversationState, new_state):
                session_context.current_state = getattr(ConversationState, new_state)

                # 保存更新后的会话上下文
                await self.session_context_manager.save_session_context(session_context)

                self.logger.info(f"会话状态已更新: {session_id} -> {new_state}")
                return True
            else:
                self.logger.error(f"无效的状态名称: {new_state}")
                return False

        except Exception as e:
            self.logger.error(f"更新会话状态失败: {e}")
            return False
    
    async def handle_skip_question_and_ask_next(self, message: str, session_id: str, user_id: str, decision_result: Dict[str, Any]) -> str:
        """处理跳过问题并提出下一个"""
        try:
            return """没关系，我们可以先跳过这个问题。

让我们从另一个角度来了解您的需求：

**📊 项目背景**
1. 这个项目是为了解决什么具体问题？
2. 目前的现状是什么样的？
3. 您希望通过这个项目达到什么目标？

**👥 相关人员**
- 项目涉及哪些人员或团队？
- 主要的决策者是谁？

请从您觉得最重要的方面开始描述。"""
        except Exception as e:
            self.logger.error(f"跳过问题失败: {e}")
            return "我们换个话题，请从您最想聊的方面开始，详细描述一下您的项目想法。"
    
    async def handle_rephrase_and_inquire(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """重新表述并询问"""
        try:
            return f"""让我重新理解一下您的需求：

您提到的 "{message[:100]}..." 这个想法很有意思。

**🔄 让我换个方式问：**
1. 从用户的角度看，这个项目要解决什么痛点？
2. 如果项目成功了，最直观的改变是什么？
3. 您觉得这个项目的核心价值在哪里？

**🎯 具体场景**
- 能否举个具体的使用场景？
- 典型的用户会在什么情况下需要这个？

这样的表述是否更清楚一些？"""
        except Exception as e:
            self.logger.error(f"重新表述失败: {e}")
            return "让我换个角度理解您的需求，请用您最熟悉的方式描述一下这个项目的核心想法。"
    
    async def handle_apology_and_request(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理道歉并请求澄清"""
        try:
            return f"""抱歉，我可能没有完全理解您的意思。

您刚才提到："{message[:100]}..."

**🤔 需要澄清的地方：**
1. 这个项目的主要目标我理解对了吗？
2. 我是否遗漏了什么重要信息？
3. 您还有什么补充的要点？

**💡 建议**
请再详细解释一下项目的核心需求，我会更仔细地听取您的想法，确保我们在同一个理解层面上。

感谢您的耐心！"""
        except Exception as e:
            self.logger.error(f"道歉请求失败: {e}")
            return "抱歉我没理解清楚，请再详细说明一下您的项目需求，我会认真倾听。"
    
    async def handle_provide_suggestions(self, session_id: str, message: str, user_id: str, history: Any) -> str:
        """提供建议和方案"""
        try:
            return f"""基于您描述的需求："{message[:100]}..."

**💡 我的建议：**

**🚀 实施建议**
1. **分阶段实施**：建议将项目分为几个可管理的阶段
2. **风险控制**：识别关键风险点并准备应对方案  
3. **资源配置**：合理配置人力和技术资源

**🔧 技术方案**
- 选择合适的技术栈和架构
- 考虑可扩展性和维护性
- 建立完善的测试和部署流程

**📈 成功因素**
- 明确的需求定义和验收标准
- 有效的项目管理和沟通机制
- 持续的用户反馈和迭代优化

您觉得这些建议中哪个方面最符合您的需求？我们可以进一步深入讨论。"""
        except Exception as e:
            self.logger.error(f"提供建议失败: {e}")
            return "基于您的需求，我建议我们先明确项目的核心目标，然后制定详细的实施计划。您觉得从哪个方面开始最合适？"
    
    async def handle_request_clarification(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """请求澄清和补充信息"""
        try:
            return f"""关于您提到的："{message[:100]}..."

我需要一些额外信息来更好地帮助您：

**❓ 需要澄清的问题：**
1. **范围边界**：这个项目的具体边界是什么？包含哪些功能，不包含哪些？
2. **成功标准**：您如何定义这个项目的成功？有什么具体的衡量指标？
3. **约束条件**：有什么技术、时间、预算或其他方面的限制？

**🔍 深入了解：**
- 类似的解决方案您了解过吗？有什么不满意的地方？
- 这个项目的紧急程度如何？
- 预期的用户群体规模大概是多少？

请尽可能详细地回答这些问题，这样我能给您更精准的建议。"""
        except Exception as e:
            self.logger.error(f"请求澄清失败: {e}")
            return "为了给您更好的建议，我需要了解更多细节。请详细描述一下项目的具体需求和期望目标。"
    
    
    async def handle_reset_conversation(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理对话重置"""
        try:
            # 执行重置逻辑
            reset_success = self.reset_conversation()
            
            if reset_success:
                return """好的，我们重新开始！

我是AI需求采集助手，可以帮助您：
🎯 整理和分析业务需求
📋 制定详细的需求规格
📝 生成规范的需求文档

请告诉我您想要讨论的项目或需求，我们开始吧！"""
            else:
                return "重置过程中遇到了一些问题，但我们可以重新开始。请告诉我您的新需求。"
                
        except Exception as e:
            self.logger.error(f"处理对话重置失败: {e}")
            return "好的，我们重新开始！请告诉我您想要讨论的项目或需求。"
    
    async def handle_general_chat(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理一般聊天"""
        try:
            return "感谢您与我交流！我是专业的需求采集助手。如果您有任何业务需求需要整理，或者想了解需求分析的相关问题，我很乐意为您提供帮助。"
            
        except Exception as e:
            self.logger.error(f"处理一般聊天失败: {e}")
            return "很高兴与您交流！有什么我可以帮助您的吗？"
    
    async def handle_show_empathy_and_clarify(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理共情并澄清"""
        try:
            return "我理解您的情况。为了更好地帮助您，能否请您详细说明一下具体的需求或遇到的问题？这样我就能为您提供更精准的支持。"
            
        except Exception as e:
            self.logger.error(f"处理共情澄清失败: {e}")
            return "我理解您的需要。请告诉我更多详情，这样我就能更好地协助您。"
    
    async def handle_acknowledge_and_redirect(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理确认并重定向"""
        try:
            return "好的，我明白了。让我们回到需求收集的主题上。请告诉我您希望整理哪些业务需求，或者您想从哪个方面开始？"
            
        except Exception as e:
            self.logger.error(f"处理确认重定向失败: {e}")
            return "明白了。让我们专注于您的需求收集工作，请告诉我需要什么帮助？"
    
    async def handle_unknown_situation(self, message: str, session_id: str, decision_result: Dict[str, Any]) -> str:
        """处理未知情况"""
        try:
            return "抱歉，我可能没有完全理解您的意思。作为需求采集助手，我主要专长于帮助整理业务需求、分析功能规格和生成需求文档。您能重新描述一下您的需要吗？"
            
        except Exception as e:
            self.logger.error(f"处理未知情况失败: {e}")
            return "抱歉，请您换个方式描述，我会尽力帮助您。"
    
    async def _get_available_domains(self) -> List[Dict[str, Any]]:
        """
        获取可用领域列表
        """
        try:
            # 从知识库获取领域信息
            if hasattr(self, 'knowledge_base_agent') and self.knowledge_base_agent:
                return await self.knowledge_base_agent.get_domains()
            else:
                # 返回默认领域
                return [
                    {"domain_id": "LY_100", "name": "软件开发", "description": "软件开发相关需求"},
                    {"domain_id": "LY_200", "name": "设计创意", "description": "设计和创意相关需求"},
                    {"domain_id": "LY_300", "name": "营销推广", "description": "营销和推广相关需求"}
                ]
        except Exception as e:
            self.logger.error(f"获取领域列表失败: {e}")
            return []

    async def _get_categories_for_domain(self, domain_id: str) -> List[Dict[str, Any]]:
        """
        获取指定领域的类别列表
        """
        try:
            # 从知识库获取类别信息
            if hasattr(self, 'knowledge_base_agent') and self.knowledge_base_agent:
                return await self.knowledge_base_agent.get_categories_by_domain(domain_id)
            else:
                # 返回默认类别（根据领域）
                if domain_id == "LY_200":  # 设计创意
                    return [
                        {"category_id": "logo_design", "name": "Logo设计", "description": "品牌标识设计"},
                        {"category_id": "poster_design", "name": "海报设计", "description": "宣传海报设计"},
                        {"category_id": "ui_design", "name": "UI设计", "description": "用户界面设计"}
                    ]
                else:
                    return [
                        {"category_id": "general", "name": "通用需求", "description": "通用业务需求"}
                    ]
        except Exception as e:
            self.logger.error(f"获取类别列表失败: {e}")
            return []

    async def _get_focus_points_for_category(self, domain_id: str, category_id: str) -> List[Dict[str, Any]]:
        """
        获取指定类别的关注点列表
        """
        try:
            # 从知识库获取关注点信息
            if hasattr(self, 'knowledge_base_agent') and self.knowledge_base_agent:
                return await self.knowledge_base_agent.get_focus_points_by_domain_and_category(domain_id, category_id)
            else:
                # 返回默认关注点（根据类别）
                if category_id == "poster_design":
                    return [
                        {"id": "poster_purpose", "name": "海报用途", "priority": "P0", "description": "海报的具体用途和目标"},
                        {"id": "poster_style", "name": "设计风格", "priority": "P1", "description": "期望的设计风格"},
                        {"id": "poster_size", "name": "尺寸规格", "priority": "P1", "description": "海报的尺寸要求"},
                        {"id": "poster_content", "name": "内容要素", "priority": "P0", "description": "需要包含的文字和图像内容"}
                    ]
                else:
                    return [
                        {"id": "general_purpose", "name": "项目目标", "priority": "P0", "description": "项目的主要目标"},
                        {"id": "general_requirements", "name": "具体需求", "priority": "P1", "description": "具体的功能需求"}
                    ]
        except Exception as e:
            self.logger.error(f"获取关注点列表失败: {e}")
            return []

    async def _generate_first_collection_question(self, message: str, domain_result: Dict[str, Any], category_result: Dict[str, Any], focus_points: List[Dict[str, Any]], session_id: str, user_id: str, emotion: str = "neutral") -> str:
        """
        生成第一个采集问题（按照正确流程：信息提取 -> 状态更新 -> 问题生成）
        """
        try:
            # 根据 domain_id 获取 domain_name
            domain_id = domain_result.get("domain_id")
            domain_name = "相关领域"  # 默认值

            if domain_id:
                try:
                    # 从知识库获取领域信息
                    domains = await self._get_available_domains()
                    for domain in domains:
                        if domain.get("domain_id") == domain_id:
                            domain.get("name", "相关领域")
                            break
                except Exception as e:
                    self.logger.warning(f"获取领域名称失败: {e}")

            category_result.get("category_name", "需求")

            # 🔥 关键修复：首先进行信息提取，避免用户输入的内容已经覆盖了关注点的内容
            self.logger.info(f"[关注点流程] 开始信息提取，分析用户输入是否已覆盖关注点内容")

            # 1. 获取对话历史
            conversation_history = await self._get_recent_conversation_history(session_id, user_id)

            # 2. 调用信息提取器，基于对话历史提取信息
            if not self.information_extractor_agent:
                self.logger.warning("[关注点流程] 信息提取器未初始化，跳过信息提取步骤")
                extraction_result = {
                    "updated_summary_json": '{"extracted_points": []}',
                    "extracted_points": []
                }
            else:
                extraction_result = await self.information_extractor_agent.extract_values(
                    user_input=message,
                    focus_points=focus_points,
                    conversation_history=conversation_history
                )

            # 防御性检查：确保extraction_result是字典类型
            if not isinstance(extraction_result, dict):
                self.logger.error(f"信息提取器返回了非字典类型的结果: {type(extraction_result)}, 内容: {extraction_result}")
                extraction_result = {
                    "updated_summary_json": '{"extracted_points": []}',
                    "extracted_points": []
                }

            # 记录提取结果详情
            extracted_points = extraction_result.get("extracted_points", [])
            self.logger.info(f"[关注点流程] 信息提取完成，提取到 {len(extracted_points)} 个关注点信息")
            for i, point in enumerate(extracted_points, 1):
                point_name = point.get("name", "未知关注点")
                point_value = point.get("value", "")
                point_completeness = point.get("completeness", 0.0)
                self.logger.info(f"  [{i}] {point_name}: {point_value} (完整度: {point_completeness})")

            # 3. 更新权威的状态源：conversation_summaries 表
            new_summary_json = extraction_result.get("updated_summary_json", '{"extracted_points": []}')
            await self.summary_manager.update_summary(session_id, user_id, new_summary_json)

            # 4. 将新的摘要状态同步到关注点状态管理数据库
            focus_points_map = {fp["name"]: fp for fp in focus_points}
            summary_data = json.loads(new_summary_json)
            latest_points_status = summary_data.get("extracted_points", [])

            # 遍历最新状态，并更新 concern_point_coverage 表
            for point_status in latest_points_status:
                point_name = point_status.get("name")
                if point_name in focus_points_map:
                    point_id = focus_points_map[point_name].get("id")
                    completeness = point_status.get("completeness", 0.0)
                    extracted_value = point_status.get("value", "")

                    # 确保extracted_value是字符串类型，避免数据库绑定错误
                    if isinstance(extracted_value, dict):
                        extracted_value = json.dumps(extracted_value, ensure_ascii=False)
                    elif not isinstance(extracted_value, str):
                        extracted_value = str(extracted_value)

                    # 根据完整度决定状态 - 🔥 修复：正确的状态转换逻辑
                    threshold = ConversationConstants.get_completeness_threshold()  # 0.8
                    if completeness >= threshold:
                        status = "completed"
                    else:
                        # 如果完整度不够，检查当前状态
                        current_statuses = await self.state_manager.focus_point_manager.load_focus_points_status(session_id, user_id)
                        current_status = current_statuses.get(point_id, {}).get("status", "pending")
                        # 如果当前是processing，保持processing；否则设为pending
                        status = "processing" if current_status == "processing" else "pending"

                    await self.state_manager.update_focus_point_status(
                        session_id=session_id,
                        user_id=user_id,
                        point_id=point_id,
                        status=status,
                        value=extracted_value
                    )

                    self.logger.info(f"[关注点流程] 更新关注点状态: {point_name} -> {status} (完整度: {completeness})")

            # 5. 检查是否所有P0/P1级别的关注点都已完成
            all_required_covered = await self.state_manager.check_all_required_covered(session_id, user_id, focus_points)
            if all_required_covered:
                self.logger.info(f"[关注点流程] 所有必需关注点已完成，生成文档")
                document_content = await self._generate_document(session_id, user_id)
                return self.unified_config_loader.get_message_template("system.document.confirmation_prefix") + document_content

            # 6. 获取下一个待处理的关注点（按优先级顺序）
            selected_point = await self.state_manager.get_next_pending_point(session_id, user_id, focus_points)

            if selected_point:
                self.logger.info(f"[关注点流程] 找到下一个待处理关注点: {selected_point.get('name')}")
                self.logger.debug(f"[DEBUG] selected_point 完整内容: {selected_point}")

                # 🔥 关键修复：设置关注点为正在处理状态
                point_id = selected_point.get('id')
                self.logger.debug(f"[DEBUG] 提取的 point_id: {point_id}")
                if point_id:
                    await self.state_manager.set_point_processing_safely(session_id, user_id, point_id)
                    self.logger.info(f"[关注点流程] 已设置关注点 {selected_point.get('name')} 为 processing 状态")
                else:
                    self.logger.error(f"[ERROR] selected_point 中没有 id 字段！selected_point: {selected_point}")

                # 🔥 使用智能问题生成替代硬编码模板
                self.logger.debug(f"[第一次收集] 代码已更新，准备使用智能问题生成")
                self.logger.info(f"🚀 [第一次收集] 准备使用智能问题生成")

                if self.integrated_reply_system and self.integrated_reply_system.reply_factory:
                    self.logger.info(f"✅ [第一次收集] 智能问题生成条件满足，开始执行")
                    try:
                        # 获取项目类型和进度信息
                        project_type = ""
                        if focus_points and len(focus_points) > 0:
                            first_point = focus_points[0]
                            category_name = first_point.get('category_name', '')
                            domain_name = first_point.get('domain_name', '')
                            if category_name and domain_name:
                                project_type = f"{domain_name}-{category_name}"
                            elif category_name:
                                project_type = category_name
                            elif domain_name:
                                project_type = domain_name

                        progress_info = await self._get_progress_info(session_id, user_id, focus_points)
                        self.logger.info(f"🎯 [进度信息] 生成的进度信息: '{progress_info}'")

                        # 获取对话历史用于智能问题生成
                        conversation_history = await self._get_recent_conversation_history(session_id, user_id)
                        user_input_for_generation = message or self.unified_config_loader.get_message_template("business.question.user_context")

                        # 🚀 使用优化的一步式问题生成（合并生成+优化）
                        self.logger.info(f"✅ [第一次收集] 使用优化问题生成 - 关注点: {selected_point['name']}, 项目类型: {project_type}, 用户情绪: {emotion}")
                        base_question = await self.integrated_reply_system.reply_factory.generate_optimized_question(
                            name=selected_point['name'],
                            description=selected_point['description'],
                            required=selected_point.get('required', False),
                            examples=selected_point.get('example', ''),
                            project_type=project_type,
                            progress_info=progress_info,
                            user_input=user_input_for_generation,
                            session_id=session_id,
                            user_id=user_id,
                            emotion=emotion,  # 传递情绪信息
                            conversation_history=conversation_history
                        )

                        self.logger.info(f"✅ [第一次收集] 智能问题生成成功，长度: {len(base_question) if base_question else 0}")

                        # 验证智能问题生成的结果
                        if not base_question or len(base_question.strip()) == 0:
                            self.logger.warning("❌ [第一次收集] 智能问题生成返回空结果，使用回退方案")
                            raise ValueError("智能问题生成返回空结果")

                    except Exception as e:
                        self.logger.warning(f"❌ [第一次收集] 智能问题生成失败，使用回退方案: {e}")
                        # 回退到硬编码模板
                        focus_point_name = selected_point.get('name', '核心需求')
                        description = selected_point.get('description', '请详细描述您的具体需求')
                        base_question = f"关于「{focus_point_name}」，{description}"
                        if 'example' in selected_point and selected_point['example']:
                            base_question += f"\n\n💡 **参考示例**：{selected_point['example']}"
                else:
                    self.logger.warning(f"❌ [第一次收集] 智能问题生成条件不满足，使用回退方案")
                    # 回退到硬编码模板
                    focus_point_name = selected_point.get('name', '核心需求')
                    description = selected_point.get('description', '请详细描述您的具体需求')
                    base_question = f"关于「{focus_point_name}」，{description}"
                    if 'example' in selected_point and selected_point['example']:
                        base_question += f"\n\n💡 **参考示例**：{selected_point['example']}"

            else:
                self.logger.info(f"[关注点流程] 没有找到待处理关注点，使用通用问题")
                base_question = self.unified_config_loader.get_message_template("user_interaction.defaults.requirement_prompt")

            # 注意：由于使用了 generate_optimized_question，问题已经一步到位优化完成，无需额外优化步骤
            self.logger.info(f"✅ [第一次收集] 问题生成完成 - 最终问题长度: {len(base_question)}")
            return base_question

        except Exception as e:
            self.logger.error(f"生成第一个采集问题失败: {e}", exc_info=True)
            return "感谢您的信息！请详细描述一下您的具体需求，我会为您提供专业的指导。"

    async def initialize_focus_points(self, session_id: str, user_id: str, focus_points: List[Dict[str, Any]]) -> None:
        """
        初始化关注点状态

        Args:
            session_id: 会话ID
            user_id: 用户ID
            focus_points: 关注点列表
        """
        try:
            # 这里应该将关注点状态保存到数据库或会话状态中
            # 暂时只记录日志
            self.logger.info(f"初始化关注点状态 - session: {session_id}, user: {user_id}, 关注点数量: {len(focus_points)}")
            for point in focus_points:
                self.logger.debug(f"关注点: {point.get('name')} (优先级: {point.get('priority')})")
        except Exception as e:
            self.logger.error(f"初始化关注点状态失败: {e}", exc_info=True)

    # ==================== 工具方法 ====================
    
    def get_agent_info(self) -> Dict[str, Any]:
        """获取Agent信息"""
        return {
            "agent_type": "ConversationFlowAgent",
            "session_id": self.session_id,
            "dependencies": {
                "config_service": self.config_service is not None,
                "llm_service": self.llm_service is not None,
                "message_manager": self.message_manager is not None,
                "document_manager": self.document_manager is not None,
                "focus_point_manager": self.focus_point_manager is not None,
                "database_manager": self.database_manager is not None,
                "document_generator": self.document_generator_agent is not None,
                "knowledge_base_agent": self.knowledge_base_agent is not None,
                "intent_decision_engine": self.intent_decision_engine is not None,
                "state_manager": self.state_manager is not None,
                "session_manager": self.session_manager is not None,
                "message_processor": self.message_processor is not None,
            },
            "current_state": self.get_conversation_state(),
            "initialization_time": datetime.now().isoformat()
        }


# ==================== 工厂函数 ====================

def create_conversation_flow_agent_with_dependencies(session_id: str, **overrides) -> AutoGenConversationFlowAgent:
    """
    使用Agent工厂创建ConversationFlowAgent
    
    Args:
        session_id: 会话ID
        **overrides: 覆盖默认依赖的参数
        
    Returns:
        配置好的ConversationFlowAgent实例
    """
    from ..factory import agent_factory
    return agent_factory.get_conversation_flow_agent(session_id, **overrides)


# 导出主要类和函数
__all__ = [
    'AutoGenConversationFlowAgent',
    'create_conversation_flow_agent_with_dependencies',
]
