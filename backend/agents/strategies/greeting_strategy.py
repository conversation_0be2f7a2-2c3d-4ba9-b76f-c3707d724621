"""
问候策略实现

处理用户的问候消息，包括：
- 基本问候（你好、hi、hello等）
- 时间相关问候（早上好、下午好等）
- 礼貌性问候回应

优先级: 9 (最高)
支持意图: greeting
"""

import logging
from typing import List

from ..decision_types import DecisionStrategy, AnalyzedContext, DecisionResult, create_decision_result
from ..unified_state_manager import ConversationState
from backend.config.unified_config_loader import get_unified_config
from backend.config.keywords_loader import get_keywords_loader


class GreetingStrategy(DecisionStrategy):
    """问候策略实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = get_unified_config()
        self.keywords_loader = get_keywords_loader()

        # 从配置加载问候关键词
        self.greeting_keywords = self.keywords_loader.get_keywords("interaction_keywords.greeting", [])
    
    @property
    def name(self) -> str:
        return "greeting_strategy"
    
    @property
    def supported_intents(self) -> List[str]:
        return ["greeting"]
    
    @property
    def priority(self) -> int:
        return 9  # 最高优先级
    
    async def can_handle(self, context: AnalyzedContext) -> bool:
        """
        判断是否能处理当前上下文
        
        Args:
            context: 分析后的上下文
            
        Returns:
            bool: 是否能处理
        """
        # 1. 基于意图判断
        if context.intent == "greeting":
            return True
        
        # 2. 基于关键词判断（双重保险）
        message_lower = context.message.lower()
        for keyword in self.greeting_keywords:
            if keyword in message_lower:
                return True
        
        # 3. 如果是会话开始且消息很短，可能是问候
        if (context.current_state == ConversationState.IDLE and 
            len(context.message.strip()) <= 10):
            return True
        
        return False
    
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """
        执行问候策略
        
        Args:
            context: 分析后的上下文
            
        Returns:
            DecisionResult: 决策结果
        """
        try:
            # 选择合适的问候回复
            response_template = self._select_greeting_response(context)
            
            # 计算置信度
            confidence = await self.calculate_confidence(context)
            
            # 创建决策结果
            result = create_decision_result(
                action="send_greeting",
                confidence=confidence,
                intent=context.intent,
                emotion=context.emotion,
                response_template=response_template,
                next_state=ConversationState.IDLE,  # 问候后保持空闲状态
                strategy_name=self.name,
                metadata={
                    "greeting_type": self._detect_greeting_type(context),
                    "is_first_interaction": context.current_state == ConversationState.IDLE,
                    "message_length": len(context.message)
                }
            )
            
            self.logger.info(f"问候策略执行成功: {context.message[:20]}...")
            return result
            
        except Exception as e:
            self.logger.error(f"问候策略执行失败: {e}", exc_info=True)
            
            # 返回基础问候结果
            fallback_greeting = self.config.get_message_template("greeting.simple")
            return create_decision_result(
                action="send_greeting",
                confidence=0.7,
                intent="greeting",
                emotion="neutral",
                response_template=fallback_greeting,
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={"error": str(e), "fallback": True}
            )
    
    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """
        计算处理置信度
        
        Args:
            context: 分析后的上下文
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence_factors = []
        
        # 1. 意图匹配度
        if context.intent == "greeting":
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.3)
        
        # 2. 关键词匹配度
        message_lower = context.message.lower()
        keyword_matches = sum(1 for keyword in self.greeting_keywords if keyword in message_lower)
        if keyword_matches > 0:
            confidence_factors.append(min(0.8, 0.4 + keyword_matches * 0.2))
        else:
            confidence_factors.append(0.2)
        
        # 3. 消息长度因子（问候通常较短）
        message_length = len(context.message.strip())
        if message_length <= 5:
            confidence_factors.append(0.8)  # 很短，可能是问候
        elif message_length <= 15:
            confidence_factors.append(0.6)  # 中等长度
        else:
            confidence_factors.append(0.3)  # 较长，不太像纯问候
        
        # 4. 会话状态因子
        if context.current_state == ConversationState.IDLE:
            confidence_factors.append(0.7)  # 空闲状态更可能是问候
        else:
            confidence_factors.append(0.4)  # 其他状态问候可能性较低
        
        # 计算加权平均
        return sum(confidence_factors) / len(confidence_factors)
    
    def _select_greeting_response(self, context: AnalyzedContext) -> str:
        """选择合适的问候回复"""
        message_lower = context.message.lower()

        # 1. 检查时间相关问候
        if "早上好" in message_lower or "早安" in message_lower:
            return self.config.get_message_template("greeting.basic")
        elif "下午好" in message_lower:
            return self.config.get_message_template("greeting.friendly")
        elif "晚上好" in message_lower or "晚安" in message_lower:
            return self.config.get_message_template("greeting.professional")

        # 2. 检查情感状态
        if context.emotion == "positive":
            return self.config.get_message_template("greeting.service_ready")
        elif context.emotion == "negative":
            return self.config.get_message_template("greeting.professional")

        # 3. 默认选择基础问候
        return self.config.get_message_template("greeting.basic")
    
    def _detect_greeting_type(self, context: AnalyzedContext) -> str:
        """检测问候类型"""
        message_lower = context.message.lower()
        
        if any(time_word in message_lower for time_word in ["早上", "下午", "晚上", "早安", "晚安"]):
            return "time_based"
        elif any(formal_word in message_lower for formal_word in ["您好", "请问"]):
            return "formal"
        elif any(casual_word in message_lower for casual_word in ["hi", "hello", "嗨", "hey"]):
            return "casual"
        else:
            return "basic"
    
    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """
        计算与上下文的匹配度
        
        Args:
            context: 分析后的上下文
            
        Returns:
            float: 匹配度 (0.0-1.0)
        """
        if context.intent in self.supported_intents:
            return 1.0
        
        # 基于关键词的匹配度
        message_lower = context.message.lower()
        keyword_matches = sum(1 for keyword in self.greeting_keywords if keyword in message_lower)
        
        if keyword_matches > 0:
            return min(0.8, keyword_matches * 0.3)
        
        return 0.0


# 导出策略类
__all__ = ['GreetingStrategy']
