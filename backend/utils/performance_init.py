"""
性能监控初始化模块

此模块提供性能监控系统的初始化和关闭功能。
主要功能包括：
1. 初始化性能监控器
2. 配置性能监控参数
3. 启动后台监控任务
4. 优雅关闭监控系统
"""

import os
import logging
from typing import Dict, Any, Optional
from backend.config.unified_config_loader import get_unified_config
from .performance_monitor import performance_monitor, PerformanceMonitor

logger = logging.getLogger(__name__)


def init_performance_monitoring(
    enabled: bool = True,
    data_dir: Optional[str] = None,
    auto_save: bool = True,
    save_interval: int = 300,
    high_concurrency_mode: bool = False
) -> PerformanceMonitor:
    """
    初始化性能监控系统

    Args:
        enabled: 是否启用性能监控
        data_dir: 性能数据保存目录，默认使用项目根目录下的 logs/performance
        auto_save: 是否自动保存性能数据
        save_interval: 自动保存间隔（秒）
        high_concurrency_mode: 是否启用高并发模式

    Returns:
        PerformanceMonitor: 性能监控器实例
    """
    try:
        # 设置默认数据目录
        if data_dir is None:
            # 使用项目根目录下的logs/performance
            from ..config.settings import PERFORMANCE_LOG_DIR
            data_dir = str(PERFORMANCE_LOG_DIR)

        # 确保数据目录存在
        if enabled:
            os.makedirs(data_dir, exist_ok=True)
            logger.info(f"性能监控数据目录: {data_dir}")

        # 在高并发模式下调整监控参数
        if high_concurrency_mode:
            # 减少采样频率以降低性能影响
            save_interval = max(save_interval, 600)  # 至少10分钟保存一次
            logger.info("高并发模式下调整性能监控参数: 增加保存间隔以降低性能影响")

        # 重新初始化性能监控器（如果需要）
        if not performance_monitor._initialized or not performance_monitor.enabled:
            # 关闭现有监控器
            if performance_monitor._initialized:
                performance_monitor.shutdown()

            # 重新初始化
            performance_monitor.__init__(
                data_dir=data_dir,
                enabled=enabled,
                auto_save=auto_save,
                save_interval=save_interval
            )

        if enabled:
            logger.info("性能监控系统初始化完成")
            logger.info(f"- 数据目录: {data_dir}")
            logger.info(f"- 自动保存: {auto_save}")
            logger.info(f"- 保存间隔: {save_interval}秒")
            if high_concurrency_mode:
                logger.info("- 高并发模式: 已启用")
        else:
            logger.info("性能监控系统已禁用")

        return performance_monitor

    except Exception as e:
        logger.error(f"初始化性能监控系统失败: {str(e)}")
        raise


def shutdown_performance_features():
    """关闭性能监控功能"""
    try:
        if performance_monitor._initialized:
            # 保存最终的性能数据
            if performance_monitor.enabled:
                performance_monitor.save_metrics("final_performance_report.json")
            
            # 关闭监控器
            performance_monitor.shutdown()
            logger.info("性能监控系统已关闭")
        else:
            logger.info("性能监控系统未初始化，无需关闭")
            
    except Exception as e:
        logger.error(f"关闭性能监控系统时出错: {str(e)}")


# 为了向后兼容，提供别名函数
def init_performance_monitor(
    enabled: bool = True,
    data_dir: Optional[str] = None,
    auto_save: bool = True,
    save_interval: int = 300
) -> PerformanceMonitor:
    """
    初始化性能监控系统（别名函数）

    这是 init_performance_monitoring 的别名，用于向后兼容
    """
    return init_performance_monitoring(enabled, data_dir, auto_save, save_interval)


def get_performance_config() -> Dict[str, Any]:
    """
    获取性能监控配置

    Returns:
        Dict[str, Any]: 当前配置信息
    """
    return {
        "enabled": performance_monitor.enabled,
        "data_dir": performance_monitor.data_dir,
        "auto_save": performance_monitor.auto_save,
        "save_interval": performance_monitor.save_interval,
        "initialized": performance_monitor._initialized
    }


def get_business_performance_config() -> Dict[str, Any]:
    """
    从business_rules.yaml获取性能优化配置

    Returns:
        Dict[str, Any]: 性能优化配置
    """
    try:
        # 获取性能配置，如果不存在则返回默认值
        unified_config = get_unified_config()
        performance_config = unified_config.get_business_rule("system.performance", {})

        # 设置默认值
        default_config = {
            "caching": {
                "enable_caching": True,
                "cache_ttl": 3600,
                "max_cache_size": 1000,
                "cleanup_strategy": "lru"
            },
            "database": {
                "enable_query_cache": True,
                "query_cache_ttl": 300,
                "max_batch_size": 100,
                "connection_pool_size": 10,
                "slow_query_threshold": 0.1,
                "log_slow_queries": True
            },
            "response_time": {
                "keyword_matching": unified_config.get_threshold("performance.timeout.short", 1),
                "semantic_matching": unified_config.get_threshold("performance.timeout.medium", 10),
                "llm_processing": unified_config.get_threshold("performance.timeout.very_long", 500),
                "state_transition": unified_config.get_threshold("performance.timeout.default", 5),
                "database_operations": unified_config.get_threshold("performance.timeout.database", 100)
            },
            "resource_limits": {
                "concurrent_sessions_per_user": unified_config.get_threshold("limits.max_concurrent", 5),
                "max_message_length": unified_config.get_threshold("limits.max_query_length", 10000),
                "max_document_size": unified_config.get_threshold("limits.cache_max_size", 1048576),
                "max_conversation_turns": unified_config.get_threshold("limits.max_history_items", 100)
            },
            "monitoring": {
                "enable_monitoring": True,
                "save_interval": 300,
                "auto_save": True,
                "warning_thresholds": {
                    "llm_call_duration": unified_config.get_threshold("performance.timeout.default", 5.0),
                    "db_query_duration": unified_config.get_threshold("performance.timeout.short", 1.0),
                    "api_response_duration": unified_config.get_threshold("performance.timeout.medium", 10.0),
                    "error_rate": unified_config.get_threshold("quality.similarity_threshold", 1.0)
                }
            },
            "cleanup": {
                "auto_cleanup": True,
                "inactive_session_timeout": 30,
                "document_retention_versions": 10,
                "log_retention_days": 30
            }
        }

        # 合并配置（业务配置覆盖默认配置）
        def merge_config(default: dict, business: dict) -> dict:
            result = default.copy()
            for key, value in business.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_config(result[key], value)
                else:
                    result[key] = value
            return result

        return merge_config(default_config, performance_config)

    except Exception as e:
        logger.error(f"获取业务性能配置失败: {str(e)}")
        # 返回默认配置
        return {
            "caching": {"enable_caching": True, "cache_ttl": 3600, "max_cache_size": 1000},
            "database": {"enable_query_cache": True, "connection_pool_size": 10},
            "monitoring": {"enable_monitoring": True, "save_interval": 300, "auto_save": True}
        }





def get_performance_status() -> Dict[str, Any]:
    """
    获取性能监控状态
    
    Returns:
        Dict[str, Any]: 状态信息
    """
    return {
        "monitoring_active": performance_monitor._initialized and performance_monitor.enabled,
        "config": get_performance_config(),
        "metrics_count": {
            "api_metrics": len(performance_monitor.api_metrics),
            "llm_metrics": len(performance_monitor.llm_metrics),
            "db_metrics": len(performance_monitor.db_metrics)
        },
        "resource_monitoring": not performance_monitor.stop_monitoring,
        "auto_save_active": performance_monitor.auto_save and performance_monitor.enabled
    }
