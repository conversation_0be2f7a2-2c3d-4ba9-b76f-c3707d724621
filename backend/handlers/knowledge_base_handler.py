#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库处理器

专门处理知识库查询相关的Action，集成RAG知识库代理
"""

import logging
from typing import List, Dict, Any
from backend.handlers.base_action_handler import <PERSON><PERSON><PERSON><PERSON>and<PERSON>, ActionContext, ActionResult
from backend.config.unified_config_loader import get_unified_config


class KnowledgeBaseHandler(BaseActionHandler):
    """知识库处理器"""
    
    def __init__(self, conversation_flow, rag_agent=None):
        super().__init__(conversation_flow)
        self.rag_agent = rag_agent
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @property
    def supported_actions(self) -> List[str]:
        """返回支持的actions列表"""
        return [
            "search_knowledge_base",
            "query_faq",
            "explain_feature",
            "provide_tutorial"
        ]

    async def can_handle(self, action: str, context) -> bool:
        """检查是否可以处理指定的action"""
        return action in self.supported_actions
    
    async def handle(self, context: ActionContext) -> ActionResult:
        """处理知识库相关的Action"""
        try:
            self.logger.info(f"处理知识库Action: {context.action}")
            
            if context.action == "search_knowledge_base":
                return await self._handle_knowledge_search(context)
            elif context.action == "query_faq":
                return await self._handle_faq_query(context)
            elif context.action == "explain_feature":
                return await self._handle_feature_explanation(context)
            elif context.action == "provide_tutorial":
                return await self._handle_tutorial_request(context)
            else:
                return self.create_error_result(f"不支持的知识库action: {context.action}")
                
        except Exception as e:
            self.logger.error(f"处理知识库Action失败: {e}")
            return self.create_error_result(f"知识库查询失败: {e}")
    
    async def _handle_knowledge_search(self, context: ActionContext) -> ActionResult:
        """处理知识库搜索"""
        try:
            self.logger.info(f"执行知识库搜索: {context.message}")

            # 检查RAG代理是否可用
            if not self.rag_agent:
                self.logger.debug("RAG代理未初始化，尝试从AgentFactory获取")
                # 尝试从AgentFactory获取RAG代理
                try:
                    from backend.agents.factory import agent_factory
                    self.rag_agent = agent_factory.get_rag_knowledge_base_agent()
                    if self.rag_agent:
                        self.logger.info("成功从AgentFactory获取RAG代理")
                except Exception as e:
                    self.logger.warning(f"从AgentFactory获取RAG代理失败: {e}")

                if not self.rag_agent:
                    self.logger.warning("无法获取RAG代理，使用回退处理")
                    return await self._handle_fallback_response(context)

            # 执行RAG查询
            try:
                rag_result = await self.rag_agent.query(context.message)

                if hasattr(rag_result, 'success') and rag_result.success and hasattr(rag_result, 'answer') and rag_result.answer:
                    self.logger.info(f"知识库查询成功，返回答案长度: {len(rag_result.answer)}")

                    # 构建成功响应
                    return self.create_success_result(
                        content=rag_result.answer,
                        action_type="knowledge_search",
                        metadata={
                            "sources": getattr(rag_result, 'sources', []),
                            "processing_info": getattr(rag_result, 'processing_info', {}),
                            # 添加分类结果，避免使用演示数据
                            "domain_result": self._infer_domain_from_knowledge_search(context.message),
                            "category_result": self._infer_category_from_knowledge_search(context.message)
                        }
                    )
                else:
                    self.logger.info("知识库中未找到相关信息，使用回退处理")
                    return await self._handle_fallback_response(context)

            except Exception as rag_error:
                self.logger.error(f"RAG查询失败: {rag_error}")
                return await self._handle_fallback_response(context)

        except Exception as e:
            self.logger.error(f"知识库搜索失败: {e}")
            return await self._handle_fallback_response(context)
    
    async def _handle_faq_query(self, context: ActionContext) -> ActionResult:
        """处理FAQ查询"""
        try:
            # 这里可以实现专门的FAQ查询逻辑
            # 暂时使用知识库搜索
            return await self._handle_knowledge_search(context)
            
        except Exception as e:
            self.logger.error(f"FAQ查询失败: {e}")
            return self.create_error_result(f"FAQ查询失败: {e}")
    
    async def _handle_feature_explanation(self, context: ActionContext) -> ActionResult:
        """处理功能说明请求"""
        try:
            # 这里可以实现专门的功能说明逻辑
            return await self._handle_knowledge_search(context)
            
        except Exception as e:
            self.logger.error(f"功能说明失败: {e}")
            return self.create_error_result(f"功能说明失败: {e}")
    
    async def _handle_tutorial_request(self, context: ActionContext) -> ActionResult:
        """处理教程请求"""
        try:
            # 这里可以实现专门的教程提供逻辑
            return await self._handle_knowledge_search(context)
            
        except Exception as e:
            self.logger.error(f"教程提供失败: {e}")
            return self.create_error_result(f"教程提供失败: {e}")
    
    def _infer_domain_from_knowledge_search(self, message: str) -> Dict[str, Any]:
        """从知识库搜索中推断领域分类"""
        # 知识库查询不应该推断领域分类，返回None让系统使用正常的分类流程
        self.logger.debug("知识库查询不推断领域分类，返回None")
        return None

    def _infer_category_from_knowledge_search(self, message: str) -> Dict[str, Any]:
        """从知识库搜索中推断类别分类"""
        # 知识库查询不应该推断类别分类，返回None让系统使用正常的分类流程
        self.logger.debug("知识库查询不推断类别分类，返回None")
        return None

    async def _handle_fallback_response(self, context: ActionContext) -> ActionResult:
        """处理回退响应"""
        try:
            # 当知识库查询失败时的回退处理
            config = get_unified_config()
            content = config.get_message_template("error.knowledge_base_not_found")
            
            return self.create_success_result(
                content=content,
                action_type="knowledge_fallback"
            )
            
        except Exception as e:
            self.logger.error(f"回退处理失败: {e}")
            return self.create_error_result("系统暂时无法处理您的请求，请稍后再试。")
