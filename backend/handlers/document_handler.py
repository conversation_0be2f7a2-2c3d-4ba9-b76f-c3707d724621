#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档处理器

处理文档相关的actions
"""

from typing import List
from .base_action_handler import BaseActionHandler, ActionContext, ActionResult
from backend.config.unified_config_loader import get_unified_config


class DocumentHandler(BaseActionHandler):
    """文档处理器"""
    
    @property
    def supported_actions(self) -> List[str]:
        return [
            "execute_document_modification",
            "modify_document",  # 支持简化决策引擎的action名称
            "confirm_document"  # 支持文档确认
        ]
    
    async def can_handle(self, action: str, context: ActionContext) -> bool:
        return action in self.supported_actions
    
    async def handle(self, context: ActionContext) -> ActionResult:
        """处理文档相关的action"""
        
        if not await self.validate_context(context):
            return self.create_error_result("上下文验证失败")
        
        try:
            if context.action in ["execute_document_modification", "modify_document"]:
                return await self._handle_document_modification(context)
            elif context.action == "confirm_document":
                return await self._handle_document_confirmation(context)
            else:
                return self.create_error_result(f"不支持的action: {context.action}")
                
        except Exception as e:
            self.logger.error(f"处理action {context.action} 失败: {e}", exc_info=True)
            return self.create_error_result(str(e))
    
    async def _handle_document_modification(self, context: ActionContext) -> ActionResult:
        """处理文档修改"""
        try:
            # 直接调用review_and_refine_agent处理文档修改
            if self.conversation_flow.review_and_refine_agent is None:
                from backend.agents.review_and_refine import AutoGenReviewAndRefineAgent
                self.conversation_flow.review_and_refine_agent = AutoGenReviewAndRefineAgent(
                    llm_client=self.conversation_flow.llm_client,
                    agent_name="review_and_refine",
                    user_id=context.user_id
                )

            refine_result = await self.conversation_flow.review_and_refine_agent.process_message({
                "text": context.message,
                "session_id": context.session_id,
                "user_id": context.user_id
            })

            if refine_result.get("success"):
                next_action = refine_result.get("next_action")
                if next_action == "completed":
                    # 重置状态
                    from backend.agents.session_context import SessionContext
                    session_context = SessionContext(
                        session_id=context.session_id,
                        user_id=context.user_id
                    )
                    await self.conversation_flow.reset_state(session_context)
                    result = await self.conversation_flow._get_document_finalized_message()
                elif next_action == "clarify":
                    # 处理澄清请求，保持DOCUMENTING状态
                    result = refine_result.get("text_response", "需要更多信息来完成修改。")
                elif next_action == "review_again":
                    # 在修改后的文档中添加操作指引
                    result = refine_result.get("text_response", "文档修改完成。")
                    modified_document = refine_result.get("modified_document", "")

                    if modified_document:
                        # 添加操作指引
                        from backend.config.unified_config_loader import get_unified_config
                        doc_guidance = get_unified_config().get_message_template("system.document.guidance")

                        # 如果响应包含文档内容，在文档后添加指引
                        if modified_document in result:
                            result = result.replace(modified_document, modified_document + doc_guidance)
                        else:
                            # 如果响应不包含完整文档，直接在末尾添加指引
                            result += doc_guidance
                else:
                    result = refine_result.get("text_response", "文档修改完成。")
            else:
                config = get_unified_config()
                result = config.get_message_template("error.document_modification")

            return self.create_success_result(
                content=result,
                action_type="document_modification"
            )

        except Exception as e:
            self.logger.error(f"文档修改失败: {e}")
            return self.create_error_result(f"文档修改失败: {e}")

    async def _handle_document_confirmation(self, context: ActionContext) -> ActionResult:
        """处理文档确认"""
        try:
            self.logger.info(f"处理文档确认 - session: {context.session_id}")

            # 获取确认成功的消息模板
            from backend.config.unified_config_loader import get_unified_config
            confirmation_message = get_unified_config().get_message_template(
                "document.confirmed",
                default="✅ 太好了！需求文档已确认完成。\n\n感谢您的配合，如果后续有新的需求，随时可以联系我！"
            )

            return self.create_success_result(
                content=confirmation_message,
                action_type="document_confirmation"
            )

        except Exception as e:
            self.logger.error(f"文档确认失败: {e}")
            return self.create_error_result(f"文档确认失败: {str(e)}")
