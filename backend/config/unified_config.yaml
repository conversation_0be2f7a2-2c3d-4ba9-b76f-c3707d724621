# ============================================================================
# 统一配置文件 (unified_config.yaml) - 系统配置中心
# ============================================================================
#
# 作用：整合所有分散的配置文件，提供统一的配置管理
# 
# 设计原则：
# 1. 单一数据源：所有配置集中在一个文件中
# 2. 层次清晰：按功能模块组织配置
# 3. 向后兼容：保持与现有配置的兼容性
# 4. 易于维护：清晰的注释和结构
#
# 配置模块：
# - system: 系统基础配置 (版本、语言、日志、性能设置)
# - llm: LLM客户端配置 (模型连接、场景映射、参数配置)
# - conversation: 对话管理配置 (关键词规则、加速策略)
# - business_rules: 业务规则配置 (重试机制、需求采集、文档确认)
# - message_templates: 消息模板配置 (系统消息、用户交互、错误处理)
# - database: 数据库配置 (连接设置、查询语句、表配置)
# - strategies: 决策引擎策略 (意图处理、状态转换、回复生成)
# - knowledge_base: 知识库配置 (ChromaDB、检索、安全设置)
# - performance: 性能配置 (缓存、并发、监控)
# - security: 安全配置 (输入验证、数据保护、访问控制)
#
# 🔧 使用方式：
#   from backend.config.unified_config_loader import get_unified_config
#   config = get_unified_config()
#   # 获取配置值：config.get_config_value("path.to.config")
#   # 获取LLM配置：config.get_llm_config("scenario_name")
#   # 获取消息模板：config.get_message_template("template.path")
#   # 获取数据库查询：config.get_database_query("table.query_name")
#
# 🔐 环境变量支持：
#   格式：${ENV_VAR_NAME} 或 ${ENV_VAR_NAME:-default_value}
#   必需环境变量：DEEPSEEK_API_KEY, DOUBAO_API_KEY, QWEN_API_KEY, OPENROUTER_API_KEY
#
# ⚠️ 维护注意事项：
#   1. 修改配置后需要重启服务生效
#   2. 敏感信息必须使用环境变量，不要硬编码API密钥
#   3. 保持配置路径的一致性，避免在代码中硬编码路径
#   4. 添加新配置时，请同时更新相关文档和测试
#   5. 定期运行 python tools/config_validator.py 检查配置一致性
# ============================================================================

# ============================================================================
# 系统基础配置 (System Configuration)
# ============================================================================
# 用途：定义系统的基本运行参数、日志配置和性能设置
# 访问方式：config.get_config_value("system.debug_mode")
# ============================================================================
system:
  version: "3.0"                                    # 配置文件版本号
  description: "需求采集系统统一配置"                  # 系统描述
  last_updated: "2025-07-20"                        # 最后更新日期

  # 基础设置 - 系统语言和功能开关
  language: "zh-CN"                                 # 系统默认语言
  supported_languages: ["zh-CN", "en-US"]          # 支持的语言列表
  fallback_enabled: true                           # 启用回退机制
  debug_mode: false                                # 调试模式开关 (生产环境应为false)

  # 日志配置 - 控制系统日志的输出格式和存储
  logging:
    level: "INFO"                                  # 日志级别 (DEBUG/INFO/WARNING/ERROR)
    format: "json"                                 # 日志格式 (json/text)
    max_file_size: "10MB"                          # 单个日志文件最大大小
    backup_count: 5                                # 保留的日志文件数量

  # 决策引擎配置 - 统一决策引擎的选择和参数
  decision_engine:
    type: "simplified"                             # 决策引擎类型 (simplified/unified/adapter)
    enable_caching: true                           # 启用决策结果缓存
    cache_ttl: 300                                 # 决策缓存生存时间(秒)
    fallback_to_simplified: true                   # 失败时回退到简化引擎

  # 性能设置 - 系统性能相关的参数配置
  performance:
    llm_timeout: 10                                # LLM调用超时时间(秒)
    max_retry_attempts: 3                          # 最大重试次数
    cache_enabled: true                            # 启用缓存机制
    cache_ttl: 3600                                # 缓存生存时间(秒)
    max_conversation_turns: 15                     # 单次对话最大轮数
    max_message_length: 1000                       # 单条消息最大长度

# ============================================================================
# LLM客户端配置 (LLM Configuration)
# ============================================================================
# 用途：配置各种LLM模型的连接参数和场景映射关系
# 访问方式：
#   - 通过LLMConfigManager: config_manager.get_model_config(agent_name="scenario")
#   - 直接访问: config.get_config_value("llm.models.deepseek-chat.temperature")
# 重要：不要在代码中硬编码模型名称，应通过场景映射获取配置
# ============================================================================
llm:
  # 默认模型 - 当场景映射中没有指定模型时使用
  default_model: "deepseek-chat"

  # 模型连接配置 - 定义各个LLM提供商的连接参数
  # 格式：models.{model_name}.{parameter}
  models:
    # DeepSeek模型 - 高性价比的代码和推理模型
    deepseek-chat:
      provider: "deepseek"                         # 提供商标识
      api_key: "${DEEPSEEK_API_KEY}"               # API密钥(环境变量)
      api_base: "https://api.deepseek.com"         # API基础URL
      model_name: "deepseek-chat"                  # 模型名称
      temperature: 0.7                             # 生成温度(0.0-2.0)
      max_tokens: 4000                             # 最大输出token数
      top_p: 1                                     # 核采样参数
      timeout: 45                                  # 请求超时时间(秒)
      max_retries: 3                               # 最大重试次数

    # 豆包Pro模型 - 字节跳动的大语言模型，支持32K上下文
    doubao-pro-32k:
      provider: "doubao"                           # 提供商标识
      api_key: "${DOUBAO_API_KEY}"                 # API密钥(环境变量)
      api_base: "https://ark.cn-beijing.volces.com/api/v3"  # API基础URL
      model_name: "doubao-pro-32k-241215"          # 模型名称
      temperature: 0.7                             # 生成温度
      max_tokens: 8000                             # 最大输出token数
      top_p: 1.0                                   # 核采样参数
      timeout: 45                                  # 请求超时时间(秒)
      max_retries: 3                               # 最大重试次数

    doubao-1.5-Lite:
      provider: "doubao"
      api_key: "${DOUBAO_API_KEY}"
      api_base: "https://ark.cn-beijing.volces.com/api/v3"
      model_name: "doubao-1-5-lite-32k-250115"
      temperature: 0.7
      max_tokens: 8000
      top_p: 1.0
      timeout: 45
      max_retries: 3

    qwen-plus:
      provider: "qwen"
      api_key: "${QWEN_API_KEY}"
      api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model_name: "qwen-plus"
      temperature: 0.7
      max_tokens: 8000
      top_p: 1.0
      timeout: 45
      max_retries: 3

    qwen-max-latest:
      provider: "qwen"
      api_key: "${QWEN_API_KEY}"
      api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model_name: "qwen-max-latest"
      temperature: 0.7
      max_tokens: 8000
      top_p: 1.0
      timeout: 50
      max_retries: 3

    qwen-turbo-latest:
      provider: "qwen"
      api_key: "${QWEN_API_KEY}"
      api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model_name: "qwen-turbo-latest"
      temperature: 0.5
      max_tokens: 8000
      top_p: 1.0
      timeout: 45
      max_retries: 3

    qwen-intent:
      provider: "qwen"
      api_key: "${QWEN_API_KEY}"
      api_base: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model_name: "tongyi-intent-detect-v3"
      temperature: 0.7
      max_tokens: 4000
      top_p: 1.0
      timeout: 50
      max_retries: 3

    openrouter-gemini-flash:
      provider: "openrouter"
      api_key: "${OPENROUTER_API_KEY}"
      api_base: "https://openrouter.ai/api/v1"
      model_name: "google/gemini-2.5-flash"
      temperature: 0.7
      max_tokens: 7000
      top_p: 1.0
      timeout: 45
      max_retries: 3

  # ============================================================================
  # 场景到模型的映射 (Scenario Mapping)
  # ============================================================================
  # 用途：定义不同业务场景使用哪个具体的LLM模型
  # 访问方式：通过LLMConfigManager自动根据场景获取对应模型配置
  # 重要：代码中应使用场景名称而不是直接指定模型名称
  # ============================================================================
  scenario_mapping:
    # 分类任务 - 低成本、高速度，对准确性要求较高
    domain_classifier: "doubao-1.5-Lite"          # 领域分类器
    category_classifier: "doubao-1.5-Lite"        # 类别分类器

    # 理解任务 - 需要一定理解能力，兼顾速度和成本
    intent_recognition: "doubao-pro-32k"          # 意图识别
    information_extractor: "doubao-pro-32k"       # 信息提取
    optimized_question_generation: "qwen-plus"    # 优化问题生成（一步到位）
    clarification_generator: "doubao-pro-32k"     # 澄清问题生成

    # 核心任务 - 需要强大逻辑和流程控制能力
    conversation_flow: "qwen-plus"                # 对话流程管理

    # 文档生成任务 - 需要高创造性和长文本生成能力
    document_generator: "qwen-plus"               # 文档生成器

    # 动态消息生成器 - 需要自然语言表达能力
    empathy_generator: "doubao-pro-32k"           # 共情消息生成
    greeting_generator: "doubao-pro-32k"          # 问候消息生成
    apology_generator: "doubao-pro-32k"           # 道歉消息生成
    domain_guidance_generator: "doubao-pro-32k"  # 领域引导生成

    # LLM服务默认配置
    llm_service: "deepseek-chat"                  # LLM服务默认模型

  # ============================================================================
  # 场景参数配置 (Scenario Parameters)
  # ============================================================================
  # 用途：为不同场景定制LLM调用参数（temperature、max_tokens、timeout等）
  # 访问方式：通过LLMConfigManager.get_scenario_params()获取
  # 设计原则：根据任务类型调整参数以优化性能和质量
  # ============================================================================
  scenario_params:
    # 分类任务 - 需要一致性，使用保守参数
    domain_classifier:
      temperature: 0.3
      max_tokens: 1500
      timeout: 20
    category_classifier:
      temperature: 0.3
      max_tokens: 1500
      timeout: 20

    # 理解任务 - 需要准确性，使用中等参数
    intent_recognition:
      temperature: 0.3
      max_tokens: 3000
      timeout: 30
    information_extractor:
      temperature: 0.3
      max_tokens: 7000
      timeout: 45

    # 生成任务 - 需要创造性，使用较高参数
    optimized_question_generation:
      temperature: 0.5  # 降低温度以提高准确性和一致性
      max_tokens: 3500  # 优化：更紧凑的输出
      timeout: 12       # 优化：更短的超时时间
    clarification_generator:
      temperature: 0.7
      max_tokens: 5000
      timeout: 30

    # 核心任务 - 需要平衡，使用中等偏高参数
    conversation_flow:
      temperature: 0.7
      max_tokens: 4000
      timeout: 30

    # 文档生成 - 需要高创造性，使用高参数
    document_generator:
      temperature: 0.9
      max_tokens: 6000
      timeout: 60

    # 默认参数 - 当场景未定义时使用
    default:
      provider: "deepseek"                         # 默认提供商
      api_key: "${DEEPSEEK_API_KEY}"               # API密钥(环境变量)
      api_base: "https://api.deepseek.com"         # API基础URL
      model_name: "deepseek-chat"                  # 默认模型名称
      temperature: 0.7
      max_tokens: 4000
      timeout: 30
      max_retries: 3

    # LLM服务配置
    llm_service:
      temperature: 0.7
      max_tokens: 4000
      timeout: 30

    # 动态消息生成器 - 需要自然语言表达能力
    empathy_generator:
      temperature: 0.7
      max_tokens: 200
      timeout: 30
    greeting_generator:
      temperature: 0.7
      max_tokens: 150
      timeout: 30
    apology_generator:
      temperature: 0.7
      max_tokens: 200
      timeout: 30
    domain_guidance_generator:
      temperature: 0.7
      max_tokens: 300
      timeout: 30
    default_generator:
      temperature: 0.7
      max_tokens: 200
      timeout: 30

# ============================================================================
# 对话管理配置 (Conversation Management Configuration)
# ============================================================================
# 用途：配置对话状态机和状态转换规则
# 访问方式：通过ConversationStateMachine自动管理状态转换
# 核心：定义系统在不同对话阶段的状态和转换逻辑
# ============================================================================
conversation:
  # 状态管理 - 定义对话过程中的各种状态
  states:
    default: "IDLE"                                # 默认初始状态
    available:                                     # 可用状态列表
      - "IDLE"                                     # 空闲状态
      - "PROCESSING_INTENT"                        # 意图处理状态
      - "COLLECTING_INFO"                          # 信息收集状态
      - "DOCUMENTING"                              # 文档生成状态
      - "COMPLETED"                                # 完成状态
      - "DOMAIN_CLARIFICATION"                     # 领域澄清状态
      - "CATEGORY_CLARIFICATION"                   # 类别澄清状态
      - "DIRECT_SELECTION"                         # 直接选择状态

  # 状态转换规则 - 定义状态之间的转换条件
  transitions:
    IDLE:                                          # 空闲状态的转换规则
      greeting: "IDLE"                             # 问候保持空闲状态
      business_requirement: "COLLECTING_INFO"      # 业务需求转入信息收集
      domain_classification_failed: "DOMAIN_CLARIFICATION"  # 领域分类失败转入澄清
      ask_question: "IDLE"

    COLLECTING_INFO:
      provide_information: "COLLECTING_INFO"
      business_requirement: "COLLECTING_INFO"
      confirm: "DOCUMENTING"

    DOCUMENTING:
      confirm: "IDLE"
      modify: "DOCUMENTING"
      restart: "IDLE"

    DOMAIN_CLARIFICATION:                          # 领域澄清状态的转换规则
      clarification_success: "COLLECTING_INFO"     # 澄清成功转入信息收集
      clarification_failed: "DIRECT_SELECTION"     # 澄清失败转入直接选择
      restart: "IDLE"                              # 重新开始

    CATEGORY_CLARIFICATION:                        # 类别澄清状态的转换规则
      clarification_success: "COLLECTING_INFO"     # 澄清成功转入信息收集
      clarification_failed: "DIRECT_SELECTION"     # 澄清失败转入直接选择
      restart: "IDLE"                              # 重新开始

    DIRECT_SELECTION:                              # 直接选择状态的转换规则
      selection_made: "COLLECTING_INFO"            # 用户选择后转入信息收集
      restart: "IDLE"                              # 重新开始
      
  # 🔧 [关键词重构] 关键词加速配置 - 已弃用，使用 keywords_config.yaml
  # DEPRECATED: 此配置已被 backend/config/keywords_config.yaml 替代
  # 保留此配置仅为向后兼容，建议使用统一关键词配置系统
  keyword_acceleration:
    enabled: false  # 🔧 [关键词重构] 已禁用，使用统一关键词配置
    rules:
      greeting:
        keywords: ["你好", "hello", "hi", "您好"]
        intent: "greeting"

      ask_question:
        keywords: ["你能做什么", "有什么功能", "能帮我什么"]
        intent: "ask_question"

      business_requirement:
        keywords: ["我想做", "我需要", "帮我做", "制作"]
        intent: "business_requirement"

      confirm:
        keywords: ["确认", "没问题", "正确", "同意", "好的", "ok"]
        intent: "confirm"

      emotional_support:
        keywords: ["心情不好", "安慰我", "难过", "沮丧", "不开心", "郁闷"]
        intent: "emotional_support"

      general_chat:
        keywords: ["聊天", "闲聊", "随便聊聊"]
        intent: "general_chat"

# ============================================================================
# 🔧 [关键词重构] 关键词规则配置 - 已弃用，使用 keywords_config.yaml
# ============================================================================
# DEPRECATED: 此配置已被 backend/config/keywords_config.yaml 替代
# 用途：定义用于快速意图识别的关键词规则
# 访问方式：通过关键词匹配引擎自动识别用户意图
# 优势：提供快速响应，避免每次都调用LLM进行意图识别
# 注意：关键词匹配有误判风险，需要合理配置和定期优化
# 保留此配置仅为向后兼容，建议使用统一关键词配置系统
# ============================================================================
keyword_rules:
  greeting:                                        # 问候意图关键词
    - "你好"                                       # 中文问候
    - "您好"
    - "hello"                                      # 英文问候
    - "hi"
    - "嗨"

  business_requirement:                            # 业务需求意图关键词
    - "我想"                                       # 表达需求的常用词
    - "我需要"
    - "要做"
    - "想要"
    - "希望"
    - "打算"

  ask_question:
    - "什么是"
    - "如何"
    - "怎么"
    - "能否"
    - "可以"

  confirm:
    - "确认"
    - "好的"
    - "是的"
    - "对"
    - "没错"
    - "正确"

  restart:
    - "重新开始"
    - "重来"
    - "全部重来"
    - "重新"

  modify:
    - "修改"
    - "改"
    - "更改"
    - "调整"

  emotional_support:
    - "心情不好"
    - "安慰我"
    - "难过"
    - "沮丧"
    - "不开心"
    - "郁闷"
    - "情绪低落"
    - "心情差"

  general_chat:
    - "聊天"
    - "闲聊"
    - "随便聊聊"
    - "聊一聊"
    - "说说话"

# ============================================================================
# 业务规则配置 (Business Rules Configuration)
# ============================================================================
# 用途：定义系统业务逻辑相关的规则和参数
# 访问方式：config.get_business_rule("retry.max_pending_attempts")
# 支持点分割路径访问，如：retry.max_pending_attempts
# ============================================================================
business_rules:
  # Action处理器配置 - 定义系统action的处理器映射
  action_handlers:
    handler_classes:
      ConversationHandler: "backend.handlers.conversation_handler.ConversationHandler"
      RequirementHandler: "backend.handlers.requirement_handler.RequirementHandler"
      DocumentHandler: "backend.handlers.document_handler.DocumentHandler"
      CompositeHandler: "backend.handlers.composite_handler.CompositeHandler"
      GeneralRequestHandler: "backend.handlers.general_request_handler.GeneralRequestHandler"
      KnowledgeBaseHandler: "backend.handlers.knowledge_base_handler.KnowledgeBaseHandler"

  # 重试机制 - 控制系统在失败时的重试行为
  retry:
    max_pending_attempts: 3                        # 单个关注点最大重试次数
    max_total_attempts: 5                          # 总体最大重试次数
    backoff_factor: 1.5                           # 重试间隔递增因子

  # 🔧 [关键词重构] 文档确认 - 用户确认文档时的关键词识别
  # DEPRECATED: 建议使用统一关键词配置系统中的 confirmation 分类
  document_confirmation:
    confirmation_keywords:                         # 确认关键词列表
      - "确认"                                     # 中文确认词
      - "没问题"
      - "正确"
      - "同意"
      - "确认无误"
      - "批准"
      - "好的"
      - "ok"                                       # 英文确认词
      - "okay"
      - "confirm"
      - "yes"
      - "good"
      
  # 需求采集规则 - 控制需求收集过程的核心参数
  requirement_collection:
    min_focus_points: 3                            # 最少需要收集的关注点数量
    max_focus_points: 10                           # 最多允许的关注点数量
    completion_threshold: 0.8                      # 完成度阈值(0.0-1.0)
                                                   # 重要：这是系统判断需求收集完成的关键指标

  # 质量控制 - 输入内容的质量检查规则
  quality_control:
    min_input_length: 2                            # 用户输入最短长度(字符)
    max_input_length: 1000                         # 用户输入最长长度(字符)
    spam_detection_enabled: true                   # 启用垃圾信息检测

  # 关注点优先级配置 - 控制不同优先级关注点的采集行为
  focus_point_priority:
    p0: true                                       # P0优先级：必须采集，不允许跳过
    p1: true                                       # P1优先级：必须采集，3次重试后可跳过
    p2: false                                      # P2优先级：可选采集，允许跳过
                                                   # 注意：设置为true时，P2关注点也会被采集并包含在文档生成中

# ============================================================================
# 消息模板配置 (Message Templates Configuration)
# ============================================================================
# 用途：定义系统与用户交互时使用的各种消息模板
# 访问方式：config.get_message_template("system.welcome")
# 支持变量替换：使用 {variable_name} 格式，如 "用户 {user_id} 的消息"
# 整合了原 message_config.yaml 的所有内容
# ============================================================================
message_templates:
  # 系统消息 - 系统级别的通用消息模板
  system:
    welcome: |
      您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。

      💡 **我可以帮您**：
      • 📋 需求梳理：将想法整理成清晰的需求文档
      • 🎯 专业分析：提供行业经验和最佳实践建议
      • 📊 成本评估：预估项目时间和预算范围
      • 🔍 风险识别：提前发现潜在问题和解决方案

      🎨 **常见项目类型**：
      • **软件开发**：网站建设、移动App、管理系统
      • **设计服务**：Logo设计、UI界面、宣传海报
      • **营销策划**：品牌推广、活动策划、内容营销
      • **数据分析**：报表制作、数据可视化、业务分析

      ⏱️ **整个流程大约需要5-10分钟**，我会通过几个关键问题了解您的需求，最终生成专业的需求文档。

      请告诉我您想要做什么项目？我会根据您的具体情况提供专业的分析和建议。
    error: "抱歉，系统遇到了一些问题，请稍后再试。"
    timeout: "处理时间过长，请重新提交您的请求。"
    processing:
      message_received: "收到新消息: '{message}', session: {session_id}"
      general_decision_engine: "状态 {state} 使用通用意图决策引擎"
      special_state_logic: "状态 {state} 使用特殊处理逻辑"
      intent_detected: "检测到意图: {intent}"
      fallback_handling: "未匹配到特定意图，使用兜底处理: {fallback_intent}"
      current_state_action: "当前状态 {state} 执行动作: {action}"
      operation_failed: "操作执行失败，请稍后重试。"
    session:
      no_domain_info: "数据库中未找到会话 {session_id} 的领域/类别信息。"
      restart_request: "用户 {session_id} 请求重新开始对话"
      restore_success: "从数据库恢复会话状态 - 领域: {domain}, 类别: {category}"
      clear_domain_success: "领域和类别信息已清除"
      clear_messages_success: "消息历史已清除"
      reset_complete: "会话重置完成"
    state:
      transition: "状态转换: {from_state} -> {to_state}"
      update_success: "状态更新成功: {new_state}"
      db_update_success: "数据库状态更新成功"
    document:
      project_name_template: "{category_name}项目"
      project_name_default: "用户项目"
      generation_start: "开始生成文档"
      generation_error: "文档生成过程中出现错误"
      content_retrieval: "正在获取文档内容"
      content_error: "获取文档内容时出现错误"
      generated: "所有必要信息已收集完毕，我已根据您提供的信息生成了需求文档。请查看并确认："
      confirmation_prefix: "根据您提供的信息，我已生成需求文档。请查看并确认：\n\n"
      guidance: "\n\n---\n文档操作指引：\n✅ 输入'确认' - 确认文档无误并完成\n🔧 指出需要修改的部分 - 例如'修改功能描述部分'"
      finalized: "感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。"
    action_executor:
      success: "Action {action} 执行成功，耗时: {duration}s"
      failed: "Action {action} 执行失败: {error}"
    initialization:
      action_executor_success: "ActionExecutor 初始化成功"

  # 问候回复
  greeting:
    basic: "您好！我是AI需求采集助手，专门帮助您整理和分析业务需求。请问有什么需求需要帮助整理？"
    friendly: "您好！很高兴为您服务。我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？"
    professional: "您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？"
    simple: "您好！我是AI需求采集助手，请问有什么需要帮助的？"
    responses:
      - "您好！我是您的AI需求分析师，很高兴为您服务！"
      - "您好！我可以帮助您分析和整理项目需求，请告诉我您的想法。"

    # 新增：基于硬编码检测报告的问候消息模板
    ai_assistant: "您好！我是AI助手，很高兴为您服务。有什么可以帮助您的吗？"
    welcome_service: "欢迎使用我们的服务！有什么可以帮助您的吗？"
    requirement_analyst: "您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。请告诉我您想要做什么项目？"
    service_ready: "您好！我是AI需求分析师，很高兴为您服务！请告诉我您的项目需求。"
    general_assistant: "您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？"
    new_project: "您好！有什么新的项目需要我帮您分析吗？"

  # 确认类模板
  confirmation:
    reset: "好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？"
    restart: "好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？"
    document_finalized: "感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。"

  # 错误类模板
  error:
    system: "系统处理您的请求时遇到问题，请稍后再试。"
    processing: "处理您的请求时出错: {error_msg}"
    modification: "抱歉，修改文档时出现错误，请稍后再试。"
    document_generation_failed: "抱歉，文档生成失败，请稍后重试。您可以选择：\n1. 重新尝试生成文档\n2. 修改需求后重试"
    general_unknown: "抱歉，系统遇到了一个未知错误。请稍后再试，或者重新描述您的需求。"
    document_generation_not_initialized: "文档生成器未初始化，无法生成文档。"
    general_request_processing: "处理您的请求时遇到问题，请稍后再试或重新描述您的需求。"

    # 新增：基于硬编码检测报告的错误消息模板
    message_processing: "抱歉，处理消息时遇到了问题。"
    request_processing: "抱歉，无法处理您的请求。"
    document_modification: "抱歉，文档修改失败。请重新描述您的修改需求。"
    knowledge_base_not_found: "抱歉，我在知识库中没有找到相关信息。"
    general_fallback: "抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。"
    emergency_fallback: "抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。"

  # 引导类模板
  guidance:
    initial: "请您先告诉我您想做什么，例如\"我想开发一个软件\"或\"我需要设计一个Logo\"。"
    default_requirement_prompt: "请描述您的详细需求。"
    specific_requirement_help: "请描述您的具体需求，我将为您提供帮助。"

    # 主动建议模板
    proactive_suggestions:
      welcome_with_examples: |
        您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。

        💡 **常见需求类型**：
        • 📱 软件开发：移动应用、网站、系统开发
        • 🎨 设计服务：Logo设计、UI设计、宣传物料
        • 📊 数据分析：报表制作、数据可视化
        • 🛒 电商项目：网店搭建、营销策划

        请告诉我您想要做什么项目？我会根据您的需求提供专业的分析和建议。

      next_steps_suggestion: |
        很好！基于您提供的信息，我建议我们按以下步骤进行：

        📋 **接下来的步骤**：
        1. 明确项目的核心目标和预期效果
        2. 确定目标用户群体和使用场景
        3. 梳理具体的功能需求和技术要求
        4. 讨论时间安排和预算考虑

        让我们从第一步开始，请详细说明您希望通过这个项目实现什么目标？

      completion_guidance: |
        太棒了！我们已经收集了丰富的需求信息。现在我来为您生成专业的需求文档。

        📄 **文档将包含**：
        • 项目概述和目标
        • 详细需求描述
        • 功能规格说明
        • 预估时间和成本
        • 实施建议

        文档生成后，您可以：
        ✅ 确认文档内容
        🔧 指出需要修改的部分
        💬 提出补充意见

        正在为您生成文档，请稍候...

  # 澄清类模板
  clarification:
    request: "抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"
    document_refinement: "非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？"
    general_request: "抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"

    # 新增：基于硬编码检测报告的澄清请求模板
    need_more_info: "我理解您的请求，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。"
    detailed_clarification: "我理解您的问题，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。"

  # 自我介绍模板
  introduction:
    full: "您好！我是一个专注于需求分析和文档生成的AI助手。我可以帮助您梳理需求、收集关键信息，并生成规范的需求文档。无论是软件开发、UI设计还是其他项目需求，我都能提供专业的分析和整理服务。请告诉我您的需求，我们一起开始吧！"
    simple: "您好！我是AI需求采集助手，可以帮您整理和分析业务需求。请问有什么需要帮助的？"

    # 由己平台业务介绍模板
    youji_platform: |
      您好！我是由己AI助手，专门为您提供业务领域的专业建议和指导。

      🏢 **由己平台核心业务**：
      • 智能需求采集系统 - 通过AI对话收集和分析项目需求
      • 在线用工平台 - 连接企业与全球优秀人才
      • 项目管理工具 - 提供完整的项目生命周期管理
      • 智能匹配服务 - 基于AI算法的精准人才匹配

      💡 **我可以帮您**：
      • 📋 需求分析和整理
      • 🎯 项目规划和建议
      • 📊 成本和时间评估
      • 🔍 风险识别和解决方案

      请告诉我您的具体需求，我会为您提供专业的分析和建议！

  # 能力介绍
  capabilities:
    main: |
      我可以帮助您：
      1. 📋 需求分析：深入了解您的项目需求
      2. 📝 需求整理：将零散的想法整理成清晰的需求文档
      3. 💡 建议提供：基于经验提供专业建议
      4. 📊 文档生成：自动生成专业的需求文档
    full: "我的主要能力包括：\n\n1. 需求分析：帮助您梳理和明确项目需求\n2. 信息收集：通过有针对性的提问收集关键信息\n3. 文档生成：生成规范的需求文档\n4. 文档修改：根据您的反馈调整和完善文档\n\n我支持多个领域的需求分析，包括软件开发、UI/UX设计、市场营销等。请告诉我您的具体需求，我会为您提供专业的帮助。"
    simple: "我可以帮您分析需求、收集信息、生成文档。请告诉我您的具体需求。"
    explanation: "我的主要能力包括：\n\n1. 需求分析：帮助您梳理和明确项目需求\n2. 信息收集：通过有针对性的提问收集关键信息\n3. 文档生成：生成规范的需求文档\n4. 文档修改：根据您的反馈调整和完善文档\n\n我支持多个领域的需求分析，包括软件开发、UI/UX设计、市场营销等。请告诉我您的具体需求，我会为您提供专业的帮助。"

  # 帮助模板
  help:
    full: "我可以为您提供以下帮助：\n\n1. 需求梳理：帮您明确项目目标和具体需求\n2. 信息收集：通过专业问题收集关键信息\n3. 文档生成：自动生成规范的需求文档\n4. 文档优化：根据您的反馈完善文档内容\n\n请直接告诉我您的项目需求，我会引导您完成整个需求分析过程。"
    simple: "我可以帮您分析需求并生成文档。请告诉我您的项目需求。"

  # 闲聊模板
  chat:
    friendly: "很高兴与您聊天！不过，我的专长是帮助分析需求和生成文档。您有什么项目需求需要我帮忙梳理吗？"
    simple: "很高兴与您交流！请告诉我您的项目需求，我来帮您分析。"
    general: "很高兴与您聊天！不过，我的专长是帮助分析需求和生成文档。您有什么项目需求需要我帮忙梳理吗？"

  # 共情模板
  empathy:
    fallback: "我理解您的感受。请告诉我您的具体需求，我会尽力为您提供帮助。"
    negative_general: "我理解您遇到的困难，这确实让人感到沮丧。请告诉我您希望如何解决这个问题，或者您需要什么样的帮助？"

  # 需求采集
  requirement_collection:
    start: "好的，让我来帮您分析这个项目。我需要了解一些关键信息："
    continue: "很好！请继续告诉我："
    clarification: "为了更好地理解您的需求，请详细说明："
    default_prompt: "请告诉我您的具体需求，我将为您提供专业的分析和建议。"

    # 情境感知建议
    contextual_suggestions:
      software_development: |
        我注意到您想开发软件项目，这很棒！基于我的经验，我建议我们重点关注：

        🎯 **核心要素**：
        • 目标用户：谁会使用这个软件？
        • 核心功能：最重要的3-5个功能是什么？
        • 平台选择：网页版、手机App还是桌面软件？
        • 数据处理：需要存储什么数据？

        💡 **专业建议**：建议先从核心功能开始，后续可以逐步扩展。

        请先告诉我您的目标用户群体是谁？

      design_project: |
        设计项目需要特别注意视觉效果和用户体验！让我为您提供一些专业建议：

        🎨 **设计关键点**：
        • 品牌定位：想要传达什么样的品牌形象？
        • 目标受众：设计面向哪个年龄段和群体？
        • 应用场景：在哪些场合使用这个设计？
        • 风格偏好：现代简约、传统经典还是创意个性？

        💡 **专业建议**：好的设计需要平衡美观性和实用性。

        请先描述一下您希望通过这个设计传达什么样的感觉或印象？

      marketing_project: |
        营销项目的成功关键在于精准定位和有效传播！我来为您分析：

        📈 **营销要素**：
        • 目标市场：主要面向哪个市场和人群？
        • 产品特色：有什么独特的卖点？
        • 竞争环境：主要竞争对手是谁？
        • 预算范围：营销投入的大概范围？

        💡 **专业建议**：建议先做市场调研，了解目标用户的真实需求。

        请先告诉我您的产品或服务有什么独特优势？

  # 回退模板
  fallback:
    default: "我理解您的请求，请告诉我您的具体需求，我会尽力提供帮助。"

  # 用户交互模板
  user_interaction:
    defaults:
      no_history: "暂无历史信息（这是新的对话）"
      user_skip_choice: "用户选择跳过"
      requirement_prompt: |
        请描述您的详细需求。

        🎯 **建议从以下方面考虑**：
        • 项目目标：您希望解决什么问题？
        • 目标用户：主要面向哪些人群？
        • 核心功能：最重要的功能有哪些？
        • 预期效果：希望达到什么样的结果？

        💬 您可以先说出大概的想法，我会引导您逐步完善细节。
      detailed_requirement: |
        请描述您的具体需求，我将为您提供专业的帮助。

        💡 **温馨提示**：
        • 可以先说出核心想法，不必一次性说完所有细节
        • 我会根据您的描述提出针对性的问题
        • 整个过程大约需要5-10分钟，最终会生成专业的需求文档
      unknown_intent: "未知意图"
    redirect:
      business_needs: "我理解您的想法。让我们回到您的具体业务需求上，请告诉我您想要实现什么？"
    processing:
      intent_string_not_json: "意图信息不是JSON格式: {intent_info}"
      idle_modify_intent: "在IDLE状态下检测到修改意图"
      llm_success_unknown: "LLM成功生成未知情况回复: {response}..."
    instructions:
      full_prompt_unknown: "{prompt_instruction}\n\n用户输入: {message}\n意图: {intent_string}"

  # 对话相关模板
  conversation:
    restart:
      confirmation: "好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？"
    modification:
      need_more_info: "为了更好地修改文档，请您提供更具体的修改要求。"
      completed: "文档已根据您的要求进行修改。"
      idle_state_prompt: "目前没有正在处理的文档。请先告诉我您的需求，我来帮您生成文档。"
    default:
      requirement_prompt: |
        请告诉我您的具体需求，我将为您提供专业的分析和建议。

        💡 **您可以从这些方面开始**：
        • 项目的基本想法和目标
        • 希望解决的问题或需求
        • 目标用户或使用场景
        • 预期的效果或成果

        💬 **温馨提示**：不用担心描述不够专业，我会通过提问帮您逐步完善所有细节。

  # 格式化相关模板
  formatting:
    history:
      empty: "暂无历史信息（这是新的对话）"
      user_prefix: "用户: "
      ai_prefix: "AI助手: "
    status:
      unknown_focus_point: "未知关注点"

  # 业务逻辑相关模板
  business:
    focus_points:
      empty_list: "关注点列表为空"
      searching_next: "寻找下一个待处理的关注点..."
      found_next: "找到下一个待处理的关注点: {point_id} - {point_name}"
      all_completed: "所有关注点已完成采集，开始生成文档"
      generation_failed: "生成后续问题失败: {error}"
      skip_processing: "正在处理跳过问题的请求..."
      skip_no_processing: "请求跳过问题，但没有找到正在处理的关注点。"
      skip_continue: "好的，我们继续下一个问题。"

      # 进度感知建议
      progress_awareness:
        quarter_complete: |
          很好！我们已经完成了 25% 的需求收集 📊

          ✅ **已收集信息**：{completed_points}
          🔄 **进行中**：{current_point}
          ⏳ **待收集**：{remaining_points}

          💡 **小贴士**：您回答得很详细，这将帮助我生成更准确的需求文档。

        half_complete: |
          太棒了！我们已经完成了一半的需求收集 🎉

          ✅ **已完成**：{completed_count}/{total_count} 个关注点
          🎯 **当前重点**：{current_point}

          💡 **专业建议**：基于目前收集的信息，我发现您的项目有很好的可行性。继续保持这个节奏！

        three_quarters_complete: |
          非常棒！我们已经完成了 75% 的需求收集 🚀

          ✅ **几乎完成**：只剩下 {remaining_count} 个关键点需要确认
          📋 **即将生成**：专业的需求文档

          💡 **温馨提示**：最后几个问题通常是关键的实施细节，请仔细考虑。

        nearly_complete: |
          太好了！我们即将完成所有需求收集 🎯

          ✅ **已收集**：{completed_count} 个关注点
          🔚 **最后一步**：{current_point}

          💡 **即将完成**：回答完这个问题后，我就可以为您生成完整的需求文档了！
    extraction:
      point_detail: "  [{index}] {point_name}: {point_value} (完整度: {completeness})"
    question:
      # 问题生成已优化为一步到位，使用 backend/prompts/optimized_question_generation.md
      optimization_failed: "问题生成失败，使用默认问题: {error}"
      user_context: "用户希望了解相关信息"
      optimization_context: "优化问题生成使用的上下文: '{user_context}', 对话历史: '{conversation_history}'"
    suggestions:
      description_guidance: "关于「{point_name}」，您可以从以下方面思考：{description}。请告诉我您的具体想法或需求。"
      multiple_points: "我注意到还有几个重要信息需要确认：{point_names}。让我们先从最重要的开始，您希望我针对哪个方面给您一些建议呢？"
      general_guidance: "基于您目前提供的信息，我建议您可以从以下几个方面继续完善：项目的具体目标、预期的用户群体、技术实现方案等。"
      single_point: "关于「{point_name}」，我这里有一些建议供您参考：\n\n{formatted_suggestions}\n\n您觉得哪几点比较符合您的想法，或者您有其他补充吗？"
      single_point_simple: "关于「{point_name}」，我的一点建议是：\n\n{suggestions}\n\n您对此有什么看法？"
      no_suggestions: "关注点 '{point_name}' 没有配置具体的建议，使用其描述信息作为引导。"
      no_pending: "没有待处理的关注点，提供与类别相关的通用建议。"
      general_suggestions: "基于您目前提供的全部信息，我有以下几点通用建议：\n\n{suggestions}\n\n您希望我详细解释哪个方面呢？"
      general_teaching_guidance: "我理解您需要指导。让我为您提供一些实用的方法和建议，帮助您更好地准备相关信息。"

      # 智能提示和最佳实践
      smart_tips:
        budget_consideration: |
          💡 **预算规划小贴士**：
          • 建议预留 20% 的缓冲资金应对意外情况
          • 可以考虑分阶段实施，降低初期投入风险
          • 不同供应商的报价可能差异较大，建议多方比较

        timeline_planning: |
          ⏰ **时间规划建议**：
          • 复杂项目通常需要比预期多 30% 的时间
          • 建议设置几个关键里程碑节点进行进度检查
          • 考虑节假日和团队成员的时间安排

        technical_choices: |
          🔧 **技术选择指导**：
          • 优先选择成熟稳定的技术方案
          • 考虑团队的技术能力和学习成本
          • 评估长期维护和扩展的便利性

        user_experience: |
          👥 **用户体验要点**：
          • 简单易用比功能丰富更重要
          • 考虑不同用户群体的使用习惯
          • 提供清晰的操作指引和帮助信息

        risk_management: |
          ⚠️ **风险管控建议**：
          • 识别项目中的主要风险点
          • 准备备选方案和应急计划
          • 定期评估项目进展和风险状况

  # 异常处理模板
  exception:
    rephrase:
      detailed: "抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。"
    suggestions:
      fallback: "我建议您先明确项目的基本信息，比如项目类型、目标用户、主要功能等，这样我能为您提供更有针对性的建议。"
    general_request:
      processing_error: "处理您的请求时遇到问题，请稍后再试或重新描述您的需求。"

  # 日志记录模板
  logging:
    debug:
      domain_restore_attempt: "尝试从数据库恢复会话 {session_id} 的领域/类别信息。"
      no_active_state: "数据库中没有会话 {session_id} 的活动状态，保持IDLE"
      problem_statement_restored: "从历史消息恢复核心问题陈述: '{problem_statement}'"
      session_init_complete: "会话 {session_id} 初始化完成"
    info:
      state_transition_documenting: "会话 {session_id} 已转换到DOCUMENTING状态"
      state_transition_collecting: "会话 {session_id} 已转换到COLLECTING_INFO状态"
      problem_statement_recorded: "记录核心问题陈述: '{problem_statement}'"
      domain_category_saved: "已保存领域和类别信息 - 领域: {domain}, 类别: {category}"
      extraction_result: "信息提取结果: {count} 个关注点"
      unknown_point: "未知关注点"
      domain_transition_collecting: "会话 {session_id} 转换到信息收集状态"
      domain_transition_documenting: "会话 {session_id} 转换到文档编辑状态"
      reset_status: "重置会话状态完成"
      focus_points_reset: "关注点状态已重置"
    warning:
      domain_restore_failed: "无法从数据库恢复领域/类别信息，可能是表结构问题: {error}"
      conversation_history_failed: "获取对话历史失败: {error}"
      intent_config_not_found: "未找到意图 {intent} 的配置"
      dynamic_reply_empty: "DynamicReplyFactory生成的回复为空"
      dynamic_reply_not_initialized: "DynamicReplyFactory未初始化"
      unrecognized_subtype: "未识别的子类型: {sub_type}"
      llm_no_valid_unknown: "LLM未返回有效的未知情况回复"
    error:
      determine_state: "确定状态失败: {error}"
      init_collecting_state: "初始化信息收集状态失败: {error}"
      document_modification_failed: "文档修改失败: {error}"
      knowledge_base_not_initialized: "知识库代理未初始化"
      no_category_id: "领域 {domain_id} 没有类别ID"
      focus_points_not_found: "未找到领域 {domain_id} 类别 {category_id} 的关注点"
      load_focus_points_failed: "加载关注点失败: {error}"
      intent_processing_failed: "意图处理失败: {error_msg}"
      initial_question_failed: "生成初始问题失败: {error}"
      format_focus_points_failed: "格式化关注点状态失败 - session_id: {session_id}, error: {error}"
      domain_guidance_generation_failed: "生成领域引导失败: {error}"
      unknown_situation_generation_failed: "生成未知情况回复失败: {error}"

  # 提示词模板
  prompts:
    greeting:
      instruction: "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手，专门帮助用户整理和分析业务需求。然后询问用户有什么需求需要帮助整理。回复要简洁、专业、友好。"
    introduction:
      instruction: "用户询问你的功能或身份，请详细介绍你是一个专业的AI需求采集助手，说明你的主要功能和能力，包括需求分析、文档生成等。语气要专业、自信、友好。"
    chat:
      instruction: "用户在进行闲聊，请友好回应，然后自然地引导用户回到系统的核心功能上，询问他们是否有需要帮助的项目需求。保持轻松友好的语气。"
    domain_guidance:
      instruction: "这是一个全新的需求，用户的需求可能非常开放和模糊。核心目标不是猜测一个具体答案，而是通过一个高质量的引导性问题，帮助用户将想法聚焦到具体可执行的业务领域。"
    capabilities:
      instruction: "用户询问你的功能或能力，请详细介绍你的主要功能和能力，包括需求分析、信息收集、文档生成等，以及你支持的领域范围。语气要专业、友好。"
    restart:
      instruction: "用户请求重新开始对话。请确认会话已重置，并引导用户开始新的需求采集。回复要简洁、友好。"
    empathy:
      default_instruction: "用户表达了负面情绪或困难，请先表示理解和共情，然后询问是否需要帮助，或者引导对话回到核心业务上。"

  # 回退和兜底模板
  fallback:
    general: "我理解您的请求，请告诉我您的具体需求，我会尽力提供帮助。"
    requirement_prompt: "请描述您的详细需求。"
    unknown_situation: "抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。"
    emergency: "抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。"
    processing_failed: "请详细描述您的需求。"

  # 其他模板
  unknown_action: "抱歉，我好像遇到了一点内部问题，我们换个话题继续吧？"

# ============================================================================
# 阈值参数配置 (Threshold Parameters Configuration)
# ============================================================================
# 用途：统一管理系统中的各种阈值参数，避免硬编码
# 访问方式：config.get_threshold("confidence.default", 0.7)
# 设计原则：按功能分类，提供合理的默认值和多级配置
# ============================================================================
thresholds:
  # 置信度阈值配置
  confidence:
    # 基础置信度阈值
    default: 0.7                    # 默认置信度阈值
    high: 0.8                       # 高置信度阈值
    very_high: 0.9                  # 极高置信度阈值
    low: 0.6                        # 低置信度阈值
    minimum: 0.5                    # 最低置信度阈值

    # 特定场景置信度阈值
    decision_engine: 0.7            # 决策引擎置信度阈值
    intent_recognition: 0.8         # 意图识别置信度阈值
    domain_classification: 0.8      # 领域分类置信度阈值
    keyword_matching: 0.7           # 关键词匹配置信度阈值
    semantic_matching: 0.6          # 语义匹配置信度阈值

  # 性能相关阈值
  performance:
    # 超时时间配置（秒）
    timeout:
      default: 5                    # 默认超时时间
      short: 3                      # 短超时时间
      medium: 10                    # 中等超时时间
      long: 30                      # 长超时时间
      very_long: 60                 # 极长超时时间

      # 特定服务超时时间
      llm_service: 30               # LLM服务超时时间
      database: 5                   # 数据库操作超时时间
      api_request: 10               # API请求超时时间
      file_operation: 5             # 文件操作超时时间

    # 重试次数配置
    retry:
      default: 3                    # 默认重试次数
      max_attempts: 3               # 最大重试次数
      quick_retry: 2                # 快速重试次数
      persistent_retry: 5           # 持久重试次数

      # 特定操作重试次数
      llm_request: 3                # LLM请求重试次数
      database_operation: 3         # 数据库操作重试次数
      api_call: 2                   # API调用重试次数
      file_access: 2                # 文件访问重试次数

  # 数量限制配置
  limits:
    # 基础数量限制
    default_max_items: 10           # 默认最大项目数
    max_results: 50                 # 最大结果数
    max_concurrent: 5               # 最大并发数

    # 特定功能限制
    max_focus_points: 10            # 最大关注点数量
    min_focus_points: 3             # 最小关注点数量
    max_keywords: 20                # 最大关键词数量
    max_history_items: 100          # 最大历史记录数
    max_query_length: 1000          # 最大查询长度

    # 缓存和存储限制
    cache_max_size: 1000            # 缓存最大大小
    log_max_entries: 10000          # 日志最大条目数
    session_max_duration: 3600      # 会话最大持续时间（秒）

  # 质量控制阈值
  quality:
    # 输入质量阈值
    min_input_length: 2             # 最小输入长度
    max_input_length: 1000          # 最大输入长度
    min_word_count: 1               # 最小单词数
    max_word_count: 200             # 最大单词数

    # 内容质量阈值
    similarity_threshold: 0.3       # 相似度阈值
    relevance_threshold: 0.5        # 相关性阈值
    completeness_threshold: 0.8     # 完整性阈值

    # 垃圾检测阈值
    spam_detection_threshold: 0.7   # 垃圾检测阈值
    abuse_detection_threshold: 0.8  # 滥用检测阈值

  # 安全相关阈值
  security:
    # 访问控制阈值
    max_login_attempts: 5           # 最大登录尝试次数
    session_timeout: 1800           # 会话超时时间（秒）
    token_expiry: 3600              # 令牌过期时间（秒）

    # 速率限制阈值
    rate_limit_per_minute: 60       # 每分钟请求限制
    rate_limit_per_hour: 1000       # 每小时请求限制
    burst_limit: 10                 # 突发请求限制

    # 内容安全阈值
    content_filter_threshold: 0.8   # 内容过滤阈值
    sensitive_data_threshold: 0.9   # 敏感数据检测阈值

  # 业务逻辑阈值
  business:
    # 需求采集阈值
    requirement_completion_threshold: 0.8  # 需求完成度阈值
    focus_point_priority_threshold: 0.7    # 关注点优先级阈值

    # 文档生成阈值
    document_quality_threshold: 0.8        # 文档质量阈值
    template_match_threshold: 0.6          # 模板匹配阈值

    # 用户交互阈值
    response_time_threshold: 2.0           # 响应时间阈值（秒）
    user_satisfaction_threshold: 0.8       # 用户满意度阈值

# 消息回复系统配置 (整合原message_config.yaml的系统配置)
message_reply_system:
  # 系统配置
  version: "2.0"
  description: "统一消息配置文件"
  last_updated: "2025-06-28"

  # 基础设置
  language: "zh-CN"
  supported_languages: ["zh-CN", "en-US"]
  fallback_enabled: true # 启用回复失败后的兜底回复

  # 性能设置
  llm_timeout: 10 # LLM调用超时时间(秒)
  max_retry_attempts: 3 # 最大重试次数

  # 功能开关
  enable_analytics: true # 启用性能监控
  enable_a_b_testing: false # 启用A/B测试

  # 回复类别配置
  categories:
    greeting: # 问候类回复
      enabled: true # 启用问候类回复
      priority: 1 # 优先级
      fallback_template: "greeting.basic"
      description: "问候类回复"
    confirmation: # 确认类回复
      enabled: true # 启用确认类回复
      priority: 1
      fallback_template: "confirmation.reset" # 回复失败后的兜底回复
      description: "确认类回复"
    error: # 错误类回复
      enabled: true
      priority: 1
      fallback_template: "error.system"
      description: "错误类回复"
    guidance: # 引导类回复
      enabled: true
      priority: 1
      fallback_template: "guidance.initial"
      description: "引导类回复"
    clarification: # 澄清类回复
      enabled: true
      priority: 1
      fallback_template: "clarification.request"
      description: "澄清类回复"
    empathy: # 共情类回复
      enabled: true
      priority: 1
      fallback_template: "empathy.fallback"
      description: "共情类回复"
    completion: # 完成类回复
      enabled: true
      priority: 1
      fallback_template: "confirmation.document_finalized"
      description: "完成类回复"

  # 动态生成器配置
  generators: # 生成器配置
    default_generator: # 默认生成器
      agent_name: "default_generator" # Agent名称
      temperature: 0.7 # 采样温度
      max_tokens: 200 # 最大生成token数
      fallback_template: "fallback.default"
      description: "通用动态回复生成"
      enabled: true
      instruction: "根据用户输入生成合适的回复。"
    clarification_generator: # 澄清回复生成器
      agent_name: "clarification_generator"
      temperature: 0.6 # 较低温度确保准确性
      max_tokens: 300 # 澄清回复可能需要更多内容
      fallback_template: "clarification.default"
      description: "专门处理用户澄清请求的回复生成"
      enabled: true
      instruction: "基于当前项目上下文和关注点，为用户提供准确、相关的澄清解释。"
    greeting_generator: # 问候回复生成器
      agent_name: "greeting_generator"
      temperature: 0.7
      max_tokens: 150
      fallback_template: "greeting.basic"
      description: "动态问候回复"
      enabled: true
      instruction: "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手，专门帮助用户整理和分析业务需求。然后询问用户有什么需求需要帮助整理。回复要简洁、专业、友好。"
    empathy_generator: # 共情回复生成器
      agent_name: "empathy_generator"
      temperature: 0.7
      max_tokens: 200
      fallback_template: "clarification.request"
      description: "共情并澄清问题"
      enabled: true
      instruction: "用户表达了负面情绪，请先共情再引导。"
    clarification_generator: # 澄清回复生成器
      agent_name: "clarification_generator"
      temperature: 0.7
      max_tokens: 200
      fallback_template: "clarification.request"
      description: "澄清问题回复"
      enabled: true
      instruction: "请生成一个友好、专业的引导性问题，帮助用户明确需求领域。"
    introduction_generator: # 自我介绍生成器
      agent_name: "introduction_generator"
      temperature: 0.7
      max_tokens: 200
      fallback_template: "introduction.full"
      description: "自我介绍生成"
      enabled: true
      instruction: "生成一个简洁、专业的自我介绍，说明你是一个专注于需求分析和文档生成的AI助手，能够帮助用户梳理需求、生成规范文档。"
    capabilities_generator: # 能力说明生成器
      agent_name: "capabilities_generator"
      temperature: 0.7
      max_tokens: 300
      fallback_template: "capabilities.explanation"
      description: "能力说明生成"
      enabled: true
      instruction: "详细介绍你的主要功能和能力，包括需求分析、信息收集、文档生成等核心能力，以及你支持的领域范围。"
    chat_generator: # 闲聊回复生成器
      agent_name: "chat_generator"
      temperature: 0.7
      max_tokens: 150
      fallback_template: "chat.general"
      description: "闲聊回复生成"
      enabled: true
      instruction: "生成一个友好的闲聊回复，然后自然地引导用户回到系统的核心功能上，询问他们是否有需要帮助的项目需求。"

  # 分析和监控配置
  analytics: # 分析和监控配置
    enabled: true # 启用分析和监控
    track_user_satisfaction: true # 跟踪用户满意度
    track_response_time: true # 跟踪响应时间
    track_fallback_usage: true # 跟踪回退使用情况
    export_interval_hours: 24   # 导出间隔（小时）

  # A/B测试配置
  a_b_testing: # A/B测试配置
    enabled: false
    test_groups: # 测试组
      greeting:
        variant_a: "greeting.basic"
        variant_b: "greeting.friendly"
        traffic_split: 0.5

# ============================================================================
# 数据库配置 (Database Configuration)
# ============================================================================
# 用途：配置SQLite数据库连接参数、表设置和SQL查询语句
# 访问方式：
#   - 连接配置: config.get_database_config()
#   - 查询语句: config.get_database_query("table.query_name")
# 重要：所有查询都包含user_id过滤，确保数据安全隔离
# ============================================================================
database:
  # 连接配置 - SQLite数据库连接参数
  connection:
    path: "backend/data/aidatabase.db"             # 数据库文件路径
    timeout: 30                                    # 连接超时时间(秒)
    check_same_thread: false                       # 允许跨线程访问

  # 表配置 - 各数据表的特殊设置
  tables:
    conversations:                                 # 会话表配置
      auto_cleanup: true                           # 启用自动清理
      cleanup_days: 30                             # 清理30天前的数据

    documents:                                     # 文档表配置
      auto_backup: true                            # 启用自动备份
      backup_interval: 24                          # 备份间隔(小时)

    focus_points:                                  # 关注点表配置
      max_per_conversation: 20                     # 每个会话最大关注点数
      
  # 查询配置 - 数据库查询的通用参数
  queries:
    batch_size: 100                                # 批量操作大小
    max_results: 1000                              # 查询结果最大数量

    # ========================================================================
    # SQL查询语句 (Database Queries)
    # ========================================================================
    # 用途：定义所有数据库操作的SQL语句模板
    # 访问方式：config.get_database_query("messages.save_message")
    # 安全要求：所有查询必须包含user_id过滤条件，防止数据泄露
    # 参数格式：使用 ? 占位符，按顺序传入参数
    # ========================================================================

    messages: # 消息表查询 - 处理用户和系统消息的存储与检索
      get_conversation_history_limited: |
        SELECT conversation_id, user_id, sender_type, content, focus_id, message_type, created_at as timestamp
        FROM messages
        WHERE conversation_id = ? AND user_id = ? AND (message_type IS NULL OR message_type != 'summary')
        ORDER BY created_at ASC
        LIMIT ?

      save_message: |
        INSERT INTO messages (conversation_id, user_id, sender_type, content, focus_id, message_type, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)

      get_messages_by_focus: |
        SELECT conversation_id, user_id, sender_type, content, focus_id, message_type, created_at
        FROM messages
        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?
        ORDER BY created_at ASC

      get_first_user_message: |
        SELECT content
        FROM messages
        WHERE conversation_id = ? AND user_id = ? AND sender_type = 'user'
        ORDER BY created_at ASC
        LIMIT 1

      get_recent_messages: |
        SELECT conversation_id, user_id, sender_type, content, focus_id, message_type, created_at
        FROM messages
        WHERE conversation_id = ? AND user_id = ?
        ORDER BY created_at DESC
        LIMIT ?

      delete_conversation_messages: |
        DELETE FROM messages
        WHERE conversation_id = ? AND user_id = ?

    conversations:
      check_exists: |
        SELECT 1 FROM conversations
        WHERE conversation_id = ? AND user_id = ?
        LIMIT 1

      create_new: |
        INSERT OR IGNORE INTO conversations (conversation_id, user_id, status, created_at, updated_at, last_activity_at)
        VALUES (?, ?, ?, ?, ?, ?)

      update_last_activity: |
        UPDATE conversations
        SET updated_at = ?, last_activity_at = ?
        WHERE conversation_id = ? AND user_id = ?

      get_active: |
        SELECT conversation_id, user_id, domain_id, category_id, status, created_at, updated_at, last_activity_at
        FROM conversations
        WHERE last_activity_at > ? AND user_id = ?
        ORDER BY last_activity_at DESC

      get_expired: |
        SELECT conversation_id, user_id, domain_id, category_id, status, created_at, updated_at, last_activity_at
        FROM conversations
        WHERE last_activity_at < ? AND user_id = ?
        ORDER BY last_activity_at ASC

      delete_expired: |
        DELETE FROM conversations
        WHERE last_activity_at < ? AND user_id = ?

      get_info: |
        SELECT conversation_id, user_id, domain_id, category_id, status, created_at, updated_at, last_activity_at
        FROM conversations
        WHERE conversation_id = ? AND user_id = ?

      get_domain_category: |
        SELECT domain_id, category_id
        FROM conversations
        WHERE conversation_id = ? AND user_id = ?

    sessions:
      ensure_session_exists: |
        INSERT OR IGNORE INTO sessions (session_id, user_id, created_at, updated_at, status)
        VALUES (?, ?, ?, ?, 'active')

      update_session: |
        UPDATE sessions
        SET updated_at = ?, status = ?
        WHERE session_id = ? AND user_id = ?

      get_session: |
        SELECT session_id, user_id, status, created_at, updated_at
        FROM sessions
        WHERE session_id = ? AND user_id = ?

    focus_point_definitions:
      get_by_category: |
        SELECT focus_id, category_id, name, description, priority, example, required
        FROM focus_point_definitions
        WHERE category_id = ?
        ORDER BY priority ASC

      get_by_focus_id: |
        SELECT focus_id, category_id, name, description, priority, example, required
        FROM focus_point_definitions
        WHERE focus_id = ?

    concern_point_coverage:
      get_by_conversation: |
        SELECT coverage_id, conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info, updated_at, additional_data
        FROM concern_point_coverage
        WHERE conversation_id = ? AND user_id = ?
        ORDER BY updated_at ASC

      insert_coverage: |
        INSERT INTO concern_point_coverage (conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info, updated_at, additional_data)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)

      update_status: |
        UPDATE concern_point_coverage
        SET status = ?, updated_at = ?
        WHERE coverage_id = ? AND user_id = ?

      get_processing_points: |
        SELECT coverage_id, conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info, updated_at, additional_data
        FROM concern_point_coverage
        WHERE conversation_id = ? AND user_id = ? AND status = 'processing'
        ORDER BY updated_at ASC

      get_coverage_by_id: |
        SELECT coverage_id, conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info, updated_at, additional_data
        FROM concern_point_coverage
        WHERE coverage_id = ? AND user_id = ?

    summaries:
      get_summary: |
        SELECT summary_json
        FROM conversation_summaries
        WHERE conversation_id = ? AND user_id = ?

      upsert_summary: |
        INSERT OR REPLACE INTO conversation_summaries (conversation_id, user_id, summary_json, updated_at)
        VALUES (?, ?, ?, datetime('now'))

    focus_points:
      check_exists: |
        SELECT 1 FROM concern_point_coverage
        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?
        LIMIT 1

      batch_insert: |
        INSERT INTO concern_point_coverage (conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

      get_status: |
        SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at
        FROM concern_point_coverage
        WHERE conversation_id = ? AND user_id = ?

      get_single_status: |
        SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at
        FROM concern_point_coverage
        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

      update_status: |
        UPDATE concern_point_coverage
        SET status = ?, updated_at = ?
        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

      clear_processing: |
        UPDATE concern_point_coverage
        SET status = 'pending', updated_at = datetime('now')
        WHERE conversation_id = ? AND user_id = ? AND status = 'processing'

      reset_status: |
        DELETE FROM concern_point_coverage
        WHERE conversation_id = ? AND user_id = ?

      complex_update: |
        UPDATE concern_point_coverage
        SET status = ?, is_covered = ?, extracted_info = ?, attempts = attempts + 1, updated_at = ?
        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

      insert_new: |
        INSERT INTO concern_point_coverage (conversation_id, user_id, focus_id, status, attempts, is_covered, extracted_info, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

      reset_all_status: |
        UPDATE concern_point_coverage
        SET status = 'pending', updated_at = ?
        WHERE conversation_id = ? AND user_id = ?

      get_completed: |
        SELECT focus_id, status, attempts, extracted_info, is_covered, updated_at
        FROM concern_point_coverage
        WHERE conversation_id = ? AND user_id = ? AND status = 'completed'

    documents: # 文档表查询 - 处理需求文档的生成、存储和管理
      get_content: |
        SELECT document_id, conversation_id, user_id, version, content, status, feedback, created_at, updated_at
        FROM documents
        WHERE document_id = ? AND user_id = ?

      get_by_conversation: |
        SELECT document_id, conversation_id, user_id, version, content, status, feedback, created_at, updated_at
        FROM documents
        WHERE conversation_id = ? AND user_id = ?
        ORDER BY version DESC

      save_document: |
        INSERT INTO documents (document_id, conversation_id, user_id, version, content, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

      update_content: |
        UPDATE documents
        SET content = ?, status = ?, updated_at = ?
        WHERE document_id = ? AND user_id = ?

      update_status: |
        UPDATE documents
        SET status = ?, updated_at = ?
        WHERE document_id = ? AND user_id = ?

      check_exists: |
        SELECT 1 FROM documents
        WHERE document_id = ? AND user_id = ?
        LIMIT 1

      delete_document: |
        DELETE FROM documents
        WHERE document_id = ? AND user_id = ?

      get_list: |
        SELECT document_id, conversation_id, user_id, version, status, created_at, updated_at
        FROM documents
        WHERE user_id = ?
        ORDER BY updated_at DESC
        LIMIT ?

    backup: # 备份相关查询
      export_conversation: |
        SELECT c.conversation_id, c.user_id, c.domain_id, c.category_id, c.status, c.created_at, c.updated_at,
               GROUP_CONCAT(m.content, '|||') as messages,
               GROUP_CONCAT(d.content, '|||') as documents
        FROM conversations c
        LEFT JOIN messages m ON c.conversation_id = m.conversation_id AND c.user_id = m.user_id
        LEFT JOIN documents d ON c.conversation_id = d.conversation_id AND c.user_id = d.user_id
        WHERE c.conversation_id = ? AND c.user_id = ?
        GROUP BY c.conversation_id, c.user_id

# ============================================================================
# 性能配置 (Performance Configuration)
# ============================================================================
# 用途：配置系统性能相关的参数，包括缓存、并发和监控
# 访问方式：config.get_config_value("performance.cache.enabled")
# 影响：直接影响系统的响应速度和资源使用
# ============================================================================
performance:
  # 缓存配置 - 控制系统缓存行为
  cache:
    enabled: true                                  # 启用缓存功能
    ttl: 3600                                      # 缓存生存时间(秒)
    max_size: 1000                                 # 缓存最大条目数

  # 并发配置 - 控制系统并发处理能力
  concurrency:
    max_workers: 4                                 # 最大工作线程数
    queue_size: 100                                # 任务队列大小
    
  # 监控配置
  monitoring:
    enabled: true
    metrics_interval: 60
    log_slow_queries: true
    slow_query_threshold: 1.0

# ============================================================================
# 安全配置 (Security Configuration)
# ============================================================================
# 用途：配置系统安全相关的参数和策略
# 访问方式：config.get_config_value("security.input_validation.enabled")
# 重要性：直接关系到系统和用户数据的安全
# ============================================================================
security:
  # 输入验证 - 验证用户输入的安全性
  input_validation:
    enabled: true                                  # 启用输入验证
    max_length: 10000                              # 输入最大长度限制
    forbidden_patterns: []                         # 禁止的输入模式列表

  # 数据保护 - 保护敏感数据
  data_protection:
    encrypt_sensitive: true                        # 加密敏感信息
    mask_personal_info: true                       # 掩码个人信息

  # 访问控制 - 控制系统访问频率
  access_control:
    rate_limiting: true                            # 启用访问频率限制
    max_requests_per_minute: 60                    # 每分钟最大请求数

# ============================================================================
# 集成配置 (Integrations Configuration)
# ============================================================================
# 用途：配置与外部系统和服务的集成参数
# 访问方式：config.get_config_value("integrations.external_apis.openai.timeout")
# 范围：包括外部API、知识库等第三方服务集成
# ============================================================================
integrations:
  # 外部API - 第三方API服务的集成配置
  external_apis:
    openai:                                        # OpenAI API配置
      timeout: 30                                  # 请求超时时间(秒)
      retry_attempts: 3                            # 重试次数

  # 知识库 - 知识库服务集成配置
  knowledge_base:
    enabled: true                                  # 启用知识库集成
    update_interval: 3600                          # 更新间隔(秒)
    
# 开发配置
development:
  # 调试选项
  debug:
    log_requests: false
    log_responses: false
    mock_llm: false
    
  # 测试配置
  testing:
    use_test_db: false
    mock_external_apis: false

# ============================================================================
# 决策引擎策略配置 (Decision Engine Strategies)
# ============================================================================
# 用途：定义系统在不同状态和意图下的处理策略
# 访问方式：通过DecisionEngine自动根据状态和意图匹配策略
# 整合了原strategies.yaml的所有策略定义
# 策略结构：每个策略包含action(动作)、priority(优先级)、prompt_instruction(提示指令)
# ============================================================================
strategies:
  # 默认兜底策略 - 当没有匹配到特定策略时使用
  DEFAULT_STRATEGY:
    action: "handle_unknown_situation"             # 处理未知情况的动作
    priority: 0                                    # 最低优先级
    prompt_instruction: "保持中性、专业的语气进行回应。"  # LLM提示指令

  # 全局策略配置 - 跨状态的通用策略
  GLOBAL:
    # 问候意图
    greeting:
      neutral:
        action: "respond_with_greeting"
        priority: 1
        prompt_instruction: "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。"
      positive:
        action: "respond_with_enthusiastic_greeting"
        priority: 2
        prompt_instruction: "用户热情地问候，请同样热情地回应并介绍功能。"

    # 询问问题意图
    ask_question:
      # 子意图：需求相关问题
      requirement_question:
        neutral:
          action: "handle_requirement_question"
          priority: 5
          prompt_instruction: "用户询问需求相关问题，请专业地指导需求收集。"
        anxious:
          action: "handle_anxious_requirement_question"
          priority: 6
          prompt_instruction: "用户焦虑地询问需求问题，请先安抚情绪再指导。"

      # 子意图：技术问题
      technical_question:
        neutral:
          action: "handle_technical_question"
          priority: 4
          prompt_instruction: "用户提出技术问题，请提供专业的技术建议。"
        confused:
          action: "simplify_technical_explanation"
          priority: 5
          prompt_instruction: "用户对技术问题感到困惑，请用通俗语言解释。"

      # 通用回退策略
      neutral:
        action: "handle_general_question"
        priority: 3
        prompt_instruction: "用户提出了问题，请分析并提供合适的回应。"
      anxious:
        action: "handle_anxious_question"
        priority: 4
        prompt_instruction: "用户带着焦虑情绪提出问题，请先安抚用户情绪。"
      confused:
        action: "handle_confused_question"
        priority: 4
        prompt_instruction: "用户对问题感到困惑，请用简单语言帮助理清思路。"

    # 业务需求意图 (扩展原始策略的子意图支持)
    business_requirement:
      # 子意图层级配置
      marketing_requirement:
        neutral:
          action: "start_requirement_gathering"
          priority: 8
          prompt_instruction: "用户描述了营销需求。请确认理解营销目标，然后重点围绕目标受众、营销渠道、预算范围、推广周期四个核心维度开始收集。询问：'为了制定精准的营销策略，我想了解您的目标受众是谁？'"
        positive:
          action: "start_requirement_gathering"
          priority: 8
          prompt_instruction: "用户积极描述了营销需求。请热情回应，表达对项目成功的信心，然后聚焦核心问题：'太好了！让我们从最关键的问题开始：您希望通过这次营销活动达到什么具体目标？比如提升品牌知名度、增加销量还是获取新用户？'"
        anxious:
          action: "start_gentle_requirement_gathering"
          priority: 9
          prompt_instruction: "用户对营销需求感到焦虑。请温和安抚：'营销策略确实需要仔细规划，但我们可以一步步来。先从最基础的开始：能简单说说您目前面临的营销挑战是什么吗？不用担心说得不够完整。'"

      software_development:
        neutral:
          action: "start_requirement_gathering"
          priority: 8
          prompt_instruction: "用户描述了软件开发需求。请确认理解用户的开发目标，然后开始收集技术需求、功能需求、用户需求等关键信息。"

      design_requirement:
        neutral:
          action: "start_requirement_gathering"
          priority: 8
          prompt_instruction: "用户描述了设计需求。请确认理解用户的设计目标和风格偏好，然后开始收集设计项目的详细要求。"

      # 通用业务需求处理（当没有明确子意图时）
      neutral:
        action: "start_requirement_gathering"
        priority: 7
        prompt_instruction: "用户描述了业务需求。请确认理解用户的目标，然后开始系统性地收集项目的关键信息。"
      positive:
        action: "start_requirement_gathering"
        priority: 7
        prompt_instruction: "用户以积极的态度描述了业务需求。请热情地回应，确认理解需求，并开始深入收集项目信息。"
      anxious:
        action: "start_gentle_requirement_gathering"
        priority: 8
        prompt_instruction: "用户带着焦虑情绪描述业务需求。请用温和、鼓励的语气回应，强调我们会耐心地帮助他们梳理需求。"

    # 提供信息意图
    provide_information:
      neutral:
        action: "acknowledge_and_redirect"
        priority: 2
        prompt_instruction: "用户提供信息，确认收到并引导下一步。"
      positive:
        action: "acknowledge_positive_info"
        priority: 4
        prompt_instruction: "用户积极提供信息，表达赞同并继续收集。"
      anxious:
        action: "acknowledge_and_reassure"
        priority: 6
        prompt_instruction: "用户焦虑地提供信息，确认收到并安抚用户。"
      confused:
        action: "clarify_and_guide"
        priority: 6
        prompt_instruction: "用户困惑地提供信息，澄清理解并引导。"

    # 确认意图
    confirm:
      neutral:
        action: "process_confirmation"
        priority: 3
        prompt_instruction: "用户确认信息，处理确认并继续流程。"

    # 拒绝意图
    reject:
      neutral:
        action: "handle_neutral_rejection"
        priority: 7
        prompt_instruction: "用户中性地拒绝，理解并提供替代选项。"
      negative:
        action: "handle_negative_rejection"
        priority: 8
        prompt_instruction: "用户带着负面情绪拒绝，不要道歉，重新组织问题。"

    # 重置和重启意图 (添加原始策略中的重要控制意图)
    reset:
      neutral:
        action: "reset_conversation"
        priority: 10
        prompt_instruction: "用户请求重置，这是一个高优先级指令。"

    restart:
      neutral:
        action: "restart_conversation"
        priority: 10
        prompt_instruction: "用户请求重新开始对话。请确认会话已重置，并引导用户开始新的需求采集。"

    # 完成意图
    complete:
      positive:
        action: "finalize_and_reset"
        priority: 1
        prompt_instruction: "用户愉快地表示完成了，请表达感谢并愉快地结束当前任务或对话。"

    # 请求澄清意图 (扩展原始策略的澄清处理)
    request_clarification:
      # 子意图层级配置
      term_clarification:
        neutral:
          action: "explain_terminology"
          priority: 6
          prompt_instruction: "用户请求解释专业术语。请用简单易懂的语言解释术语含义，并提供相关示例。"
        confused:
          action: "simplify_terminology_explanation"
          priority: 7
          prompt_instruction: "用户对专业术语感到困惑。请用最简单的语言解释，避免使用其他专业术语。"

      question_clarification:
        neutral:
          action: "clarify_question_meaning"
          priority: 6
          prompt_instruction: "用户请求解释系统提问的含义。请重新表述问题，说明提问的目的和期望的回答类型。"
        anxious:
          action: "reassure_and_clarify_question"
          priority: 7
          prompt_instruction: "用户对系统提问感到担忧。请先安抚用户，然后详细解释问题的含义和回答方式。"

      # 无子意图的回退策略
      neutral:
        action: "provide_clarification"
        priority: 5
        prompt_instruction: "用户请求澄清或解释，但具体类型不明确。请根据上下文提供清晰、详细的解释或指导。"
      anxious:
        action: "reassuring_clarification"
        priority: 6
        prompt_instruction: "用户带着焦虑情绪请求澄清，可能担心自己理解错误。请先安抚用户，然后提供清晰的解释，强调这是正常的沟通过程。"

    # 未知意图
    unknown:
      neutral:
        action: "request_clarification"
        priority: 5
        prompt_instruction: "无法理解用户意图，礼貌地请求重新描述。"
      anxious:
        action: "gentle_clarification_request"
        priority: 6
        prompt_instruction: "用户焦虑且意图不明，温和地请求澄清。"
      confused:
        action: "supportive_clarification_request"
        priority: 6
        prompt_instruction: "用户困惑且意图不明，提供支持性的澄清请求。"

  # IDLE状态特定策略
  IDLE:
    # 状态配置
    _state_config:
      use_simplified_logic: false
      description: "空闲状态，等待用户输入"

    # 问候处理
    greeting:
      neutral:
        action: "welcome_and_introduce"
        priority: 5
        prompt_instruction: "欢迎用户并介绍需求收集流程。"

    # 业务需求处理
    business_requirement:
      neutral:
        action: "start_requirement_gathering"
        priority: 6
        prompt_instruction: "用户提出需求，开始系统性收集信息。"
      positive:
        action: "start_enthusiastic_gathering"
        priority: 7
        prompt_instruction: "用户积极地提出需求，请热情回应并开始收集。"
      anxious:
        action: "start_gentle_gathering"
        priority: 7
        prompt_instruction: "用户带着焦虑提出需求，请温和地开始收集过程。"

    # 询问问题处理
    ask_question:
      requirement_question:
        neutral:
          action: "start_focused_requirement_gathering"
          priority: 7
          prompt_instruction: "明确的需求问题，开始专注收集。"
        positive:
          action: "start_enthusiastic_gathering"
          priority: 8
          prompt_instruction: "用户积极地提出需求问题，热情回应并开始收集。"
      neutral:
        action: "start_requirement_gathering"
        priority: 5
        prompt_instruction: "通用问题处理，开始需求收集。"

  # COLLECTING_INFO状态特定策略 (扩展原始策略的信息收集处理)
  COLLECTING_INFO:
    # 状态配置
    _state_config:
      use_simplified_logic: false
      description: "信息收集状态，正在收集需求信息"

    # 处理答案意图 (原始策略中的核心处理)
    process_answer:
      neutral:
        action: "process_answer_and_ask_next"
        priority: 8
        prompt_instruction: "用户正在回答问题。这是需求收集的核心环节，请准确处理用户提供的信息，并准备下一个问题。"
      anxious:
        action: "reassure_and_process_answer"
        priority: 9
        prompt_instruction: "用户带着焦虑情绪回答问题。请先安抚用户，确认他们的回答很有价值，然后处理信息。"
      confused:
        action: "clarify_and_process_answer"
        priority: 9
        prompt_instruction: "用户困惑地回答了问题。请耐心确认理解，澄清细节，然后继续。"

    # 提供信息处理 (扩展子意图支持)
    provide_information:
      # 子意图层级配置
      answer_question:
        neutral:
          action: "process_answer_and_ask_next"
          priority: 6
          prompt_instruction: "用户回答了系统提出的问题。请仔细记录这些信息，确认理解，并根据需要继续询问相关问题。"
        positive:
          action: "process_answer"
          priority: 6
          prompt_instruction: "用户积极地回答了问题。请表达感谢，确认理解答案，并继续收集其他必要信息。"
        anxious:
          action: "reassure_and_process_answer"
          priority: 7
          prompt_instruction: "用户带着焦虑情绪回答问题。请先安抚用户，确认他们的回答很有价值，然后处理信息。"

      # 无子意图的回退策略
      neutral:
        action: "process_answer_and_ask_next"
        priority: 5
        prompt_instruction: "处理用户提供的信息，继续收集下一个关注点。"
      positive:
        action: "acknowledge_positive_and_continue"
        priority: 6
        prompt_instruction: "用户积极提供信息，表达赞同并继续收集。"
      anxious:
        action: "reassure_and_continue"
        priority: 7
        prompt_instruction: "用户焦虑地提供信息，安抚并继续收集。"
      confused:
        action: "clarify_and_continue"
        priority: 7
        prompt_instruction: "用户困惑地提供信息，澄清理解并继续。"

    # 询问问题处理 (容错规则：在信息收集中，用户的回答有时可能被误判为"提问")
    ask_question:
      neutral:
        action: "process_answer_and_ask_next"
        priority: 5
        prompt_instruction: "正在处理您的回答并准备下一个问题..."

    # 拒绝意图处理
    reject:
      negative:
        action: "request_clarification"
        priority: 8
        prompt_instruction: "用户否定了我们刚才的提议或问题。请不要直接道歉，而是重新组织一下你的问题，或者提供几个选项，并询问用户的具体想法是什么。"
      neutral:
        action: "request_clarification"
        priority: 8
        prompt_instruction: "用户表示无法提供相关信息。请理解用户的情况，然后询问是否可以跳过这个问题，或者提供一些具体的选项来帮助用户回答。"

    # 请求澄清处理 (当用户在信息收集中感到困惑并请求帮助时)
    request_clarification:
      neutral:
        action: "provide_suggestions"
        priority: 8
        prompt_instruction: "用户请求帮助、建议或指导。请根据当前对话上下文和用户的具体请求，提供实用的指导建议。如果用户请求教学（如'教我如何收集'），请提供具体的操作步骤和方法。然后继续收集流程或询问下一个问题。重要：要真正回应用户的请求，不要回避或转移话题。"
      anxious:
        action: "provide_reassuring_guidance"
        priority: 9
        prompt_instruction: "用户带着焦虑情绪请求澄清，可能担心自己理解错误或回答不当。请先安抚用户的情绪，强调没有标准答案，然后提供清晰的指导和具体的例子来帮助他们。"
      confused:
        action: "provide_step_by_step_guidance"
        priority: 9
        prompt_instruction: "用户对当前问题感到困惑并请求澄清。请用最简单的语言，分步骤地解释问题的含义，提供具体的例子，并引导用户逐步思考和回答。"

    # 跳过问题处理
    skip:
      neutral:
        action: "skip_question_and_ask_next"
        priority: 6
        prompt_instruction: "用户希望跳过当前问题。"

    # 修改信息处理
    modify:
      neutral:
        action: "process_answer_and_ask_next"
        priority: 7
        prompt_instruction: "用户想要修改之前提供的信息。请确认理解用户的修改意图，更新相关信息，然后继续收集流程。"

    # 确认处理
    confirm:
      neutral:
        action: "process_answer_and_ask_next"
        priority: 5
        prompt_instruction: "用户确认了信息或回答了问题，请处理并准备下一个问题。"

    # 完成处理
    complete:
      neutral:
        action: "process_answer_and_ask_next"
        priority: 6
        prompt_instruction: "用户表示信息已提供完毕，准备结束收集阶段。"
      positive:
        action: "process_answer_and_ask_next"
        priority: 6
        prompt_instruction: "用户愉快地表示信息已提供完毕，准备结束收集阶段。"

  # DOCUMENTING状态特定策略 (采用原始策略的简化逻辑)
  DOCUMENTING:
    # 状态配置 (启用简化的"三分法"逻辑)
    _state_config:
      # 启用简化逻辑：主要关注 confirm, restart, modify 三种核心意图
      use_simplified_logic: true
      description: "文档生成状态，正在生成或修改文档"

      # 默认行为：如果用户的输入不属于 confirm 或 restart，系统会默认它是一个"修改"请求
      fallback_action: "execute_document_modification"
      fallback_intent: "modify"

      # 意图检查顺序：严格按照 confirm -> restart -> modify 的顺序来检查用户意图
      priority_order: ["confirm", "restart", "modify"]

    # 通用请求处理 (在文档审查状态下，通用请求应该被识别但优先级较低)
    general_request:
      neutral:
        action: "acknowledge_and_redirect"
        priority: 3
        prompt_instruction: "用户在文档审查过程中提出了通用请求。请简要回应这个请求，然后礼貌地将对话引导回文档审查流程。提醒用户我们正在审查生成的文档，并询问是否对文档有任何反馈或修改建议。"

    # 确认处理
    confirm:
      neutral:
        action: "finalize_and_reset"
        priority: 6
        prompt_instruction: "用户确认文档无误。请正式结束本次需求采集流程。"
      positive:
        action: "finalize_and_reset"
        priority: 6
        prompt_instruction: "用户对最终文档表示了积极的确认。请用热情的语气庆祝项目达成一致，并正式结束本次需求采集流程。"

    # 修改处理
    modify:
      neutral:
        action: "execute_document_modification"
        priority: 7
        prompt_instruction: "用户要求修改文档，请先清晰地复述一遍你理解的修改点以进行确认，然后说明将如何执行修改。"

    # 重新开始处理
    restart:
      neutral:
        action: "restart_conversation"
        priority: 8
        prompt_instruction: "用户要求重新开始或重新生成文档。请确认用户的重新开始意图，然后重置会话状态，开始新的需求采集流程。"

    # 拒绝处理
    reject:
      negative:
        action: "apologize_and_request_refinement"
        priority: 9
        prompt_instruction: "用户明确否定了文档内容，这是一个严重的问题。请务必先真诚道歉，然后主动承担责任，并询问具体需要修改的地方，引导用户给出明确的修改意见。"

  # 回复策略配置
  reply:
    default_template: "standard"
    personalization_enabled: true

  # 状态管理策略
  state:
    auto_save: true
    session_timeout: 1800

  # 错误处理策略
  error_handling:
    retry_on_failure: true
    graceful_degradation: true

# ============================================================================
# 知识库配置 (Knowledge Base Configuration)
# ============================================================================
# 用途：配置ChromaDB向量数据库和RAG检索功能
# 访问方式：config.get_config_value("knowledge_base.performance.max_results")
# 功能：支持文档检索、语义搜索和知识增强
# ============================================================================
knowledge_base:
  # 基础设置 - 知识库的全局开关
  enabled: true                                    # 启用知识库功能

  # 功能开关 - 控制知识库的各项功能
  features:
    rag_query: true                                # 启用RAG查询功能
    intent_enhancement: false                      # 意图增强(暂未启用)
    mode_switching: false                          # 模式切换(暂未启用)
    document_ingestion: false                      # 文档摄取(暂未启用)

  # ChromaDB配置 - 向量数据库设置
  chroma_db:
    path: "backend/data/chroma_db"                 # ChromaDB数据存储路径
    collection_name: "hybrid_knowledge_base"      # 集合名称
    embedding_model: "moka-ai/m3e-base"

  # 文档处理配置
  document_processing:
    chunk_size: 800
    chunk_overlap: 100
    max_chunks_per_doc: 50
    supported_formats: ["md", "txt"]

  # 检索配置
  retrieval:
    top_k: 3
    similarity_threshold: 0.3
    max_context_length: 2000

  # 安全配置
  safety:
    enable_content_filter: true
    max_query_length: 1000
    rate_limit_per_minute: 60

  # 性能配置
  performance:
    cache_enabled: true
    cache_ttl: 3600
    max_concurrent_queries: 5
    max_results: 50
    default_limit: 5

  # 角色过滤配置
  role_filters:
    enabled: false
    allowed_roles: []

  # 日志配置
  logging:
    level: "INFO"
    log_queries: false
    log_results: false
