# 配置一致性检查报告

⚠️ 发现 1365 个配置一致性问题

## 🚫 硬编码配置值

- **backend/agents/llm_service.py:625**
  ```python
  timeout = 60
  ```

- **backend/agents/review_and_refine.py:276**
  ```python
  temperature=0.5,
  ```

- **backend/agents/review_and_refine.py:277**
  ```python
  max_tokens=4096
  ```

- **backend/agents/information_extractor.py:220**
  ```python
  temperature = 0.3
  ```

- **backend/agents/information_extractor.py:221**
  ```python
  timeout = 30
  ```

- **backend/agents/domain_classifier.py:122**
  ```python
  temperature=0.5,
  ```

- **backend/agents/domain_classifier.py:123**
  ```python
  max_tokens=3000
  ```

- **backend/agents/domain_classifier.py:237**
  ```python
  temperature=0.5,
  ```

- **backend/agents/domain_classifier.py:238**
  ```python
  max_tokens=3000
  ```

- **backend/agents/conversation_flow/core_refactored.py:1100**
  ```python
  temperature=0.7,
  ```

- **backend/agents/conversation_flow/core_refactored.py:1101**
  ```python
  max_tokens=300
  ```

- **backend/agents/conversation_flow/core_refactored.py:2203**
  ```python
  temperature = 0.8
  ```

- **backend/utils/performance_monitor.py:476**
  ```python
  self.monitor_thread.join(timeout=2)
  ```

- **backend/utils/performance_monitor.py:478**
  ```python
  self.save_thread.join(timeout=2)
  ```

- **backend/api/main.py:399**
  ```python
  progress_data = await asyncio.wait_for(queue.get(), timeout=30.0)
  ```

- **backend/handlers/composite_handler.py:252**
  ```python
  max_tokens=100,
  ```

- **backend/handlers/composite_handler.py:253**
  ```python
  temperature=0.1  # 低温度，确保准确性
  ```

## 🔄 重复配置调用

- **backend/config/service.py**: 6 次配置调用
  - 第20行: `llm_config = config_service.get_llm_config("intent_recognition")`
  - 第23行: `retry_limit = config_service.get_business_rule("retry.max_pending_attempts", 3)`
  - 第26行: `template = config_service.get_message_template("greeting.basic")`
  - 第29行: `threshold = config_service.get_threshold("business_rules.requirement_collection.completion_threshold", 0.7)`
  - 第32行: `config_service.add_change_listener("keyword_config", my_callback)`

- **backend/config/__init__.py**: 5 次配置调用
  - 第17行: `llm_config = config_service.get_llm_config("intent_recognition")`
  - 第18行: `business_rule = config_service.get_business_rule("retry.max_pending_attempts", 3)`
  - 第19行: `template = config_service.get_message_template("greeting.basic")`
  - 第38行: `unified_config_manager = get_unified_config()`
  - 第39行: `config_manager = get_unified_config()`

- **backend/config/settings.py**: 4 次配置调用
  - 第37行: `_config = get_unified_config()`
  - 第148行: `params = config_service.get_scenario_params(scenario)`
  - 第158行: `"get_scenario_params() 已废弃，请使用 config_service.get_scenario_params()",`
  - 第166行: `return config_service.get_scenario_params(scenario)`

- **backend/agents/unified_llm_client_factory.py**: 4 次配置调用
  - 第67行: `config = get_unified_config()`
  - 第168行: `"model": self.config.get("model", "gpt-3.5-turbo"),`
  - 第169行: `"temperature": self.config.get("temperature", 0.7),`
  - 第170行: `"max_tokens": self.config.get("max_tokens", 1000),`

- **backend/agents/llm_service.py**: 5 次配置调用
  - 第85行: `"temperature": min(max(kwargs.get("temperature", get_unified_config().get_threshold("confidence.default", 0.7)), 0.0), 2.0),`
  - 第201行: `self.unified_config = get_unified_config()`
  - 第324行: `model_config = self.config_service.get_llm_config_with_metadata(agent_name or model_name or "default")`
  - 第555行: `model_config = self.config_service.get_llm_config_with_metadata(effective_agent_name)`
  - 第620行: `scenario_params = self.config_service.get_scenario_params(scenario) if scenario else {}`

- **backend/agents/message_reply_manager.py**: 4 次配置调用
  - 第90行: `message_config = get_unified_config().get_message_config()`
  - 第126行: `templates_config = self.config.get("templates", {})`
  - 第148行: `generators_config = self.config.get("generators", {})`
  - 第310行: `default_fallback = get_unified_config().get_message_template("guidance.default_requirement_prompt", "抱歉，我暂时无法处理您的请求。")`

- **backend/agents/simplified_decision_engine.py**: 11 次配置调用
  - 第67行: `self.config = get_unified_config()`
  - 第515行: `confidence = self.config.get_threshold("confidence.minimum", 0.0)`
  - 第532行: `confidence = self.config.get_threshold("confidence.high", 0.8)`
  - 第543行: `confidence = llm_result.get("confidence", self.config.get_threshold("confidence.high", 0.8))`
  - 第609行: `"confidence": self.config.get_threshold("confidence.minimum", 0.0),`

- **backend/agents/conversation_state_machine.py**: 12 次配置调用
  - 第42行: `self.config = get_unified_config()`
  - 第76行: `greeting_keywords = self.config.get_keyword_rules().get('greeting', {}).get('keywords', [])`
  - 第78行: `templates = self.config.get_message_templates().get('greeting', {})`
  - 第90行: `business_keywords = self.config.get_keyword_rules().get('business_requirement', {}).get('keywords', [])`
  - 第92行: `templates = self.config.get_message_templates().get('requirement_collection', {})`

- **backend/agents/rag_knowledge_base_agent.py**: 6 次配置调用
  - 第59行: `self.chroma_db_path = self.config.chroma_db.get('path', 'backend/data/chroma_db')`
  - 第60行: `self.collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')`
  - 第61行: `self.embedding_model = self.config.chroma_db.get('embedding_model', 'moka-ai/m3e-base')`
  - 第64行: `self.retrieval_config = self.config.retrieval`
  - 第70行: `self.safety_config = self.config.safety`

- **backend/agents/conversation_flow/utils.py**: 4 次配置调用
  - 第17行: `_config = get_unified_config()`
  - 第29行: `config = config_service.get_llm_config_with_metadata("conversation_flow")`
  - 第36行: `return get_unified_config().get_threshold("business_rules.requirement_collection.completion_threshold", cls.COMPLETENESS_THRESHOLD)`
  - 第40行: `return get_unified_config().get_business_rule("retry.max_pending_attempts", cls.MAX_PENDING_ATTEMPTS)`

- **backend/agents/conversation_flow/message_processor.py**: 6 次配置调用
  - 第43行: `self.config = get_unified_config()`
  - 第80行: `error_message = self.config.get_message_template("error.message_processing")`
  - 第197行: `self.logger.error(self.config.get_message_template("error.processing", error=str(e)), exc_info=True)`
  - 第198行: `error_msg = self.config.get_message_template("error.general_unknown")`
  - 第340行: `error_message = self.config.get_message_template("error.request_processing")`

- **backend/agents/conversation_flow/core_refactored.py**: 11 次配置调用
  - 第173行: `self.unified_config_loader = get_unified_config()`
  - 第177行: `llm_config = self.config_service.get_llm_config("conversation_flow")`
  - 第223行: `return self.config_service.get_message_template(`
  - 第308行: `"reply": self.config_service.get_message_template("error.system")`
  - 第514行: `quick_rules = self.config_service.get_business_rule("conversation.keyword_acceleration.rules", [])`

- **backend/agents/strategies/greeting_strategy.py**: 8 次配置调用
  - 第27行: `self.config = get_unified_config()`
  - 第112行: `fallback_greeting = self.config.get_message_template("greeting.simple")`
  - 第174行: `return self.config.get_message_template("greeting.basic")`
  - 第176行: `return self.config.get_message_template("greeting.friendly")`
  - 第178行: `return self.config.get_message_template("greeting.professional")`

- **backend/api/main.py**: 7 次配置调用
  - 第117行: `default_config = config_service.get_llm_config_with_metadata("default")`
  - 第547行: `model_config = config_service.get_llm_config_with_metadata("conversation_flow")`
  - 第820行: `db_path = get_unified_config().get_database_path()`
  - 第968行: `config = get_unified_config().get_config()`
  - 第996行: `success = get_unified_config().enable_knowledge_base()`

- **backend/data/db/focus_point_manager.py**: 10 次配置调用
  - 第37行: `get_unified_config().get_database_query("conversations.check_exists"),`
  - 第45行: `get_unified_config().get_database_query("conversations.create_new"),`
  - 第77行: `get_unified_config().get_database_query("focus_points.check_exists"),`
  - 第96行: `get_unified_config().get_database_query("focus_points.batch_insert"),`
  - 第125行: `get_unified_config().get_database_query("focus_points.get_status"),`

- **backend/data/db/message_manager.py**: 9 次配置调用
  - 第37行: `get_unified_config().get_database_query("conversations.check_exists"),`
  - 第45行: `get_unified_config().get_database_query("conversations.create_new"),`
  - 第77行: `get_unified_config().get_database_query("messages.save_message"),`
  - 第83行: `get_unified_config().get_database_query("conversations.update_last_activity"),`
  - 第110行: `get_unified_config().get_database_query("messages.get_conversation_history_limited"),`

- **backend/data/db/document_manager.py**: 9 次配置调用
  - 第33行: `query = get_unified_config().get_database_query("documents.get_content")`
  - 第48行: `query = get_unified_config().get_database_query("documents.get_by_conversation")`
  - 第63行: `query = get_unified_config().get_database_query("documents.get_by_conversation")`
  - 第101行: `query = get_unified_config().get_database_query("documents.save_document")`
  - 第152行: `query = get_unified_config().get_database_query("documents.update_content")`

- **backend/data/db/conversation_manager.py**: 6 次配置调用
  - 第50行: `get_unified_config().get_database_query("conversations.get_active"),`
  - 第79行: `get_unified_config().get_database_query("conversations.get_expired"),`
  - 第115行: `get_unified_config().get_database_query("conversations.delete_expired"),`
  - 第142行: `get_unified_config().get_database_query("backup.export_conversation"),`
  - 第174行: `get_unified_config().get_database_query("conversations.update_last_activity"),`

- **backend/handlers/conversation_handler.py**: 8 次配置调用
  - 第76行: `self.config = get_unified_config()`
  - 第190行: `content = self.config.get_message_template("system.session.reset_complete", session_id=session_id)`
  - 第237行: `prompt_instruction = self.config.get_message_template("prompts.restart.instruction")`
  - 第245行: `content = self.config.get_message_template("system.session.reset_complete")`
  - 第289行: `content = self.config.get_message_template("confirmation.document_finalized")`

- **backend/handlers/general_request_handler.py**: 7 次配置调用
  - 第19行: `self.config = get_unified_config()`
  - 第163行: `return self.config.get_message_template("clarification.need_more_info")`
  - 第180行: `result = self.config.get_message_template("greeting.general_assistant")`
  - 第440行: `result = self.config.get_message_template("clarification.detailed_clarification")`
  - 第552行: `result = self.config.get_message_template("introduction.youji_platform")`

- **backend/services/conversation_history_service.py**: 9 次配置调用
  - 第35行: `config = get_unified_config()`
  - 第58行: `self.config = get_unified_config()`
  - 第62行: `max_turns=self.config.get_threshold("system.performance.max_conversation_turns", 15),`
  - 第63行: `max_message_length=self.config.get_threshold("system.performance.max_message_length", 200),`
  - 第122行: `turns = self.config.get_threshold("performance.retry.default", 5)`

## 📁 配置文件问题

- **missing_config_file**: 缺少必需的配置文件: message_templates.yaml
- **missing_config_file**: 缺少必需的配置文件: business_rules.yaml
- **missing_config_file**: 缺少必需的配置文件: thresholds.yaml

## 🗑️ 未使用的配置键

- `message_reply_system.enable_a_b_testing`
- `message_templates.business.suggestions`
- `message_reply_system.generators.introduction_generator.max_tokens`
- `strategies.GLOBAL.business_requirement.anxious`
- `strategies.COLLECTING_INFO.request_clarification.neutral`
- `database.queries.focus_points.insert_new`
- `message_reply_system.analytics.export_interval_hours`
- `message_reply_system.generators.empathy_generator.temperature`
- `intent_system.intents.general_chat.action`
- `message_templates.formatting.history.empty`
- `security.input_validation.max_length`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious`
- `llm.scenario_params.intent_recognition.temperature`
- `strategies.COLLECTING_INFO.reject.negative.action`
- `message_reply_system`
- `knowledge_base.chroma_db.embedding_model`
- `llm.scenario_mapping.conversation_flow`
- `intent_system.state_transitions.IDLE.business_requirement`
- `system.performance.max_retry_attempts`
- `intent_system.intents.modify.description`
- `intent_system.intents.search_knowledge_base.description`
- `knowledge_base.logging.log_results`
- `message_templates.logging.error.no_category_id`
- `llm.scenario_mapping.information_extractor`
- `message_reply_system.generators.introduction_generator`
- `message_reply_system.generators.capabilities_generator`
- `intent_system.intents.modify.supported_states`
- `intent_system.intents.process_query`
- `strategies.COLLECTING_INFO.complete.neutral`
- `strategies.COLLECTING_INFO.ask_question.neutral.priority`
- `llm.models.doubao-pro-32k.api_base`
- `database.queries.summaries.get_summary`
- `llm.models.openrouter-gemini-flash.api_base`
- `strategies.GLOBAL.unknown.anxious`
- `integrations.external_apis.openai`
- `message_reply_system.generators.introduction_generator.agent_name`
- `message_templates.greeting.service_ready`
- `intent_system.intents.unknown.priority`
- `message_templates.system.document.generation_error`
- `llm.models.doubao-1.5-Lite.api_key`
- `message_templates.requirement_collection.start`
- `strategies.DEFAULT_STRATEGY.priority`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral.prompt_instruction`
- `strategies.IDLE.ask_question.neutral.priority`
- `intent_system.state_transitions.IDLE.search_knowledge_base`
- `thresholds.security.content_filter_threshold`
- `strategies.COLLECTING_INFO.skip.neutral.prompt_instruction`
- `business_rules.requirement_collection`
- `strategies.GLOBAL.ask_question.requirement_question.neutral.prompt_instruction`
- `llm.scenario_params.default.temperature`
- `knowledge_base.features`
- `strategies.GLOBAL.greeting.neutral`
- `llm.models.qwen-intent.provider`
- `intent_system.description`
- `strategies.IDLE.business_requirement.anxious.action`
- `llm.scenario_params.apology_generator`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral.priority`
- `strategies.IDLE.ask_question.neutral.action`
- `message_templates.greeting.general_assistant`
- `intent_system.version`
- `llm.models.openrouter-gemini-flash.top_p`
- `intent_system.state_transitions.COLLECTING_INFO.restart`
- `strategies.reply.default_template`
- `database.queries.documents.update_status`
- `message_reply_system.categories.greeting`
- `strategies.IDLE`
- `strategies.COLLECTING_INFO.provide_information.positive`
- `message_reply_system.a_b_testing.test_groups.greeting.variant_b`
- `thresholds.performance.timeout.default`
- `message_reply_system.generators.default_generator.temperature`
- `intent_system.intents.general_chat.supported_states`
- `message_templates.conversation.modification.completed`
- `database.queries.concern_point_coverage.get_coverage_by_id`
- `strategies.COLLECTING_INFO.process_answer`
- `message_reply_system.analytics.track_fallback_usage`
- `intent_system.intents.search_knowledge_base.examples`
- `intent_system.state_transitions.DOCUMENTING.confirm`
- `message_templates.exception.suggestions.fallback`
- `intent_system.intents.system_capability_query.description`
- `knowledge_base.safety.enable_content_filter`
- `strategies.COLLECTING_INFO.modify.neutral.action`
- `message_templates.chat`
- `llm.models.qwen-intent.max_tokens`
- `llm.models.doubao-1.5-Lite.max_tokens`
- `message_templates.system.state.transition`
- `strategies.COLLECTING_INFO.provide_information.positive.priority`
- `message_templates.user_interaction.redirect.business_needs`
- `conversation.transitions.DIRECT_SELECTION`
- `strategies.GLOBAL.ask_question.neutral`
- `thresholds.performance.timeout.llm_service`
- `intent_system.state_transitions.IDLE.greeting`
- `message_reply_system.generators.chat_generator.temperature`
- `message_templates.business.suggestions.smart_tips.timeline_planning`
- `strategies.COLLECTING_INFO.reject`
- `strategies.GLOBAL.provide_information.positive.action`
- `intent_system.intents.system_capability_query`
- `conversation.transitions.DOMAIN_CLARIFICATION`
- `llm.models.qwen-plus.timeout`
- `message_templates.system.document.guidance`
- `message_templates.business.question.user_context`
- `strategies.IDLE.ask_question.requirement_question.positive`
- `strategies.GLOBAL.unknown`
- `strategies.DOCUMENTING.confirm.neutral`
- `strategies.COLLECTING_INFO.modify.neutral`
- `strategies.GLOBAL.confirm.neutral.priority`
- `intent_system.intents.ask_question.supported_states`
- `knowledge_base.safety.rate_limit_per_minute`
- `message_templates.system.state.update_success`
- `message_reply_system.categories.confirmation.priority`
- `message_templates.system.error`
- `integrations.external_apis.openai.timeout`
- `message_reply_system.categories.empathy`
- `strategies.GLOBAL.ask_question.anxious`
- `intent_system.intents.process_query.action`
- `strategies.COLLECTING_INFO.reject.negative.priority`
- `system.performance.cache_ttl`
- `knowledge_base.retrieval`
- `message_templates.business.suggestions.smart_tips.risk_management`
- `security.data_protection`
- `message_templates.conversation.modification.idle_state_prompt`
- `integrations`
- `performance.concurrency.max_workers`
- `strategies.DOCUMENTING.confirm.neutral.prompt_instruction`
- `strategies.COLLECTING_INFO.complete.positive.action`
- `database.queries.focus_points.get_completed`
- `message_reply_system.generators.greeting_generator.temperature`
- `message_templates.prompts.capabilities`
- `strategies.DOCUMENTING.reject.negative.priority`
- `intent_system.intents.process_answer`
- `message_templates.capabilities.simple`
- `system.logging.format`
- `strategies.COLLECTING_INFO.reject.negative`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive.priority`
- `strategies.COLLECTING_INFO.confirm.neutral`
- `knowledge_base.chroma_db`
- `message_templates.business.suggestions.smart_tips.budget_consideration`
- `message_reply_system.generators.greeting_generator.fallback_template`
- `message_templates.prompts.capabilities.instruction`
- `strategies.IDLE.business_requirement.neutral.priority`
- `strategies.IDLE._state_config`
- `intent_system.intents.provide_information.description`
- `message_templates.business.suggestions.smart_tips`
- `message_reply_system.generators.clarification_generator.fallback_template`
- `strategies.GLOBAL.request_clarification.term_clarification.confused`
- `intent_system.intents.process_query.priority`
- `conversation.transitions.CATEGORY_CLARIFICATION.clarification_failed`
- `message_reply_system.categories.error.priority`
- `knowledge_base.performance.cache_ttl`
- `message_templates.help`
- `thresholds.performance`
- `message_templates.business.focus_points.skip_continue`
- `strategies.DOCUMENTING.reject`
- `thresholds.limits.max_history_items`
- `llm.models.doubao-pro-32k.temperature`
- `strategies.IDLE.greeting.neutral.prompt_instruction`
- `message_reply_system.generators.chat_generator`
- `database.tables.conversations.cleanup_days`
- `strategies.COLLECTING_INFO.skip`
- `intent_system.intents.process_answer.description`
- `thresholds.confidence.low`
- `message_reply_system.categories.completion.description`
- `strategies.DOCUMENTING.restart.neutral`
- `llm.scenario_params.clarification_generator.temperature`
- `strategies.COLLECTING_INFO.confirm`
- `thresholds.limits`
- `strategies.GLOBAL.business_requirement.positive.priority`
- `intent_system.intents.provide_information.action`
- `system.logging.level`
- `strategies.DOCUMENTING._state_config`
- `llm.scenario_params.information_extractor.max_tokens`
- `message_templates.clarification.request`
- `strategies.GLOBAL.greeting.positive.action`
- `intent_system.intents.request_clarification.description`
- `intent_system.decision_rules.priority_order`
- `strategies.COLLECTING_INFO._state_config`
- `message_templates.clarification.detailed_clarification`
- `intent_system.intents.search_knowledge_base`
- `strategies.IDLE.greeting.neutral`
- `strategies.COLLECTING_INFO.provide_information.anxious.prompt_instruction`
- `message_reply_system.categories.guidance.description`
- `message_reply_system.generators.clarification_generator.enabled`
- `intent_system.intents.process_answer.examples`
- `message_templates.system.session.clear_domain_success`
- `llm.models.qwen-turbo-latest.api_key`
- `message_reply_system.categories.guidance.priority`
- `database.queries.sessions.ensure_session_exists`
- `message_templates.greeting`
- `strategies.DOCUMENTING.general_request`
- `message_templates.guidance.default_requirement_prompt`
- `message_templates.unknown_action`
- `strategies.GLOBAL.reject.negative.action`
- `message_templates.exception`
- `strategies.DOCUMENTING.confirm`
- `llm.scenario_params.document_generator.temperature`
- `thresholds.security`
- `strategies.GLOBAL.ask_question.technical_question`
- `intent_system.intents.confirm`
- `intent_system.intents.request_clarification`
- `message_templates.business.suggestions.single_point_simple`
- `llm.models.qwen-intent.model_name`
- `strategies.GLOBAL.business_requirement.software_development.neutral.action`
- `strategies.IDLE.ask_question.neutral`
- `message_templates.formatting.history`
- `strategies.GLOBAL.request_clarification.anxious.action`
- `message_templates.chat.friendly`
- `intent_system.decision_rules.default_state`
- `llm.models.openrouter-gemini-flash.provider`
- `llm.models.doubao-1.5-Lite`
- `llm.models.qwen-max-latest.provider`
- `strategies.IDLE.ask_question.requirement_question.neutral.prompt_instruction`
- `llm.models.doubao-pro-32k.max_retries`
- `llm.scenario_params.default_generator.max_tokens`
- `llm.models.qwen-max-latest.max_tokens`
- `strategies.DOCUMENTING.general_request.neutral.action`
- `strategies.IDLE.business_requirement.positive.priority`
- `strategies.GLOBAL.greeting.positive.prompt_instruction`
- `llm.scenario_params.domain_classifier.timeout`
- `llm.scenario_params.llm_service`
- `system.decision_engine`
- `thresholds.business.user_satisfaction_threshold`
- `database.queries.sessions.update_session`
- `strategies.DOCUMENTING.confirm.positive`
- `intent_system.intents.greeting.description`
- `strategies.error_handling.retry_on_failure`
- `message_templates.user_interaction.defaults.user_skip_choice`
- `strategies.GLOBAL`
- `conversation.transitions`
- `strategies.DOCUMENTING.confirm.neutral.priority`
- `database.queries.focus_point_definitions.get_by_category`
- `message_templates.fallback.requirement_prompt`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious.prompt_instruction`
- `thresholds.performance.timeout.medium`
- `llm.models.deepseek-chat.api_key`
- `thresholds.security.session_timeout`
- `message_reply_system.categories.completion`
- `strategies.DEFAULT_STRATEGY.prompt_instruction`
- `business_rules.focus_point_priority`
- `message_templates.business.focus_points.searching_next`
- `llm.models.qwen-max-latest.temperature`
- `strategies.IDLE.business_requirement`
- `llm.scenario_params.optimized_question_generation.max_tokens`
- `intent_system.intents.greeting.supported_states`
- `llm.models.qwen-turbo-latest.model_name`
- `intent_system.state_transitions.IDLE.restart`
- `intent_system.intents.feedback.examples`
- `conversation.states.available`
- `message_templates.empathy.fallback`
- `development.debug.log_responses`
- `message_templates.system.session.restart_request`
- `message_templates.logging.error.unknown_situation_generation_failed`
- `strategies.GLOBAL.reset.neutral.prompt_instruction`
- `strategies.GLOBAL.business_requirement.software_development`
- `message_reply_system.version`
- `strategies.COLLECTING_INFO.modify.neutral.priority`
- `message_reply_system.categories.clarification.enabled`
- `thresholds.performance.timeout.file_operation`
- `message_reply_system.categories.error.enabled`
- `database.connection`
- `database.queries.messages.get_recent_messages`
- `message_templates.system.processing.current_state_action`
- `message_reply_system.llm_timeout`
- `llm.scenario_params.conversation_flow.temperature`
- `database.queries.documents.save_document`
- `message_reply_system.analytics.enabled`
- `conversation.transitions.DOCUMENTING.confirm`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral.action`
- `llm.models.openrouter-gemini-flash.timeout`
- `message_templates.user_interaction.defaults`
- `strategies.COLLECTING_INFO.provide_information.confused`
- `conversation.keyword_acceleration.rules.emotional_support`
- `message_templates.system.document.project_name_default`
- `message_reply_system.categories.completion.fallback_template`
- `intent_system.intents.emotional_support.priority`
- `database.queries.focus_points.get_status`
- `strategies.COLLECTING_INFO.provide_information.neutral.action`
- `strategies.COLLECTING_INFO.ask_question`
- `strategies.DOCUMENTING`
- `llm.scenario_params.default.api_key`
- `message_reply_system.categories.error.description`
- `llm.scenario_params.default.max_tokens`
- `llm.scenario_params.clarification_generator.max_tokens`
- `database.queries.messages.delete_conversation_messages`
- `llm.scenario_params.default.api_base`
- `message_templates.error.document_generation_not_initialized`
- `database.tables.documents.backup_interval`
- `message_templates.formatting`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral.action`
- `intent_system.state_transitions.IDLE.process_query`
- `database.queries.focus_points.reset_status`
- `thresholds.performance.timeout.very_long`
- `conversation.keyword_acceleration.rules.emotional_support.intent`
- `llm.models.qwen-turbo-latest.max_tokens`
- `database.queries.focus_points.check_exists`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral`
- `message_reply_system.categories.guidance.enabled`
- `integrations.external_apis.openai.retry_attempts`
- `intent_system.state_transitions.DOCUMENTING.restart`
- `llm.scenario_params.clarification_generator`
- `llm.models.deepseek-chat.temperature`
- `llm.scenario_params.default.model_name`
- `llm.scenario_params.conversation_flow.timeout`
- `strategies.GLOBAL.restart.neutral.prompt_instruction`
- `database.queries.sessions`
- `conversation.transitions.COLLECTING_INFO.provide_information`
- `strategies.COLLECTING_INFO.process_answer.confused.priority`
- `message_templates.logging.error.domain_guidance_generation_failed`
- `llm.models.doubao-pro-32k.model_name`
- `knowledge_base.role_filters`
- `llm.scenario_params.conversation_flow`
- `message_reply_system.categories.guidance`
- `strategies.GLOBAL.request_clarification.neutral.priority`
- `message_reply_system.max_retry_attempts`
- `message_templates.logging.info.problem_statement_recorded`
- `thresholds.business.requirement_completion_threshold`
- `message_templates.business.suggestions.general_suggestions`
- `llm.scenario_params.document_generator.timeout`
- `thresholds.limits.session_max_duration`
- `message_templates.system.processing.operation_failed`
- `strategies.COLLECTING_INFO.ask_question.neutral.action`
- `thresholds.quality.min_input_length`
- `strategies.COLLECTING_INFO.complete.positive`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral.prompt_instruction`
- `strategies.GLOBAL.request_clarification.anxious.prompt_instruction`
- `strategies.IDLE.business_requirement.positive`
- `business_rules.retry.max_total_attempts`
- `message_templates.greeting.responses`
- `strategies.GLOBAL.provide_information`
- `message_reply_system.categories.greeting.description`
- `database.queries.documents.update_content`
- `intent_system.intents.request_clarification.supported_states`
- `message_templates.business.focus_points`
- `strategies.GLOBAL.reset.neutral.action`
- `message_templates.greeting.ai_assistant`
- `keyword_rules.modify`
- `strategies.IDLE.greeting.neutral.priority`
- `message_reply_system.categories.confirmation.enabled`
- `intent_system.intents.confirm.examples`
- `strategies.state.session_timeout`
- `message_reply_system.description`
- `development.debug`
- `message_reply_system.categories.greeting.fallback_template`
- `message_reply_system.categories.confirmation.fallback_template`
- `strategies.IDLE.ask_question`
- `performance.monitoring`
- `database.tables.focus_points.max_per_conversation`
- `llm.scenario_params.default_generator.temperature`
- `message_templates.clarification`
- `message_templates.exception.rephrase`
- `llm.models.openrouter-gemini-flash.max_retries`
- `message_templates.business.focus_points.found_next`
- `database.queries.conversations.check_exists`
- `message_templates.guidance.proactive_suggestions.next_steps_suggestion`
- `intent_system.state_transitions.COLLECTING_INFO.unknown`
- `knowledge_base.logging.log_queries`
- `thresholds.limits.max_query_length`
- `message_templates.logging.warning.dynamic_reply_empty`
- `conversation.transitions.DIRECT_SELECTION.restart`
- `intent_system.state_transitions.IDLE.request_clarification`
- `message_reply_system.categories.greeting.enabled`
- `message_templates.logging.debug.problem_statement_restored`
- `strategies.IDLE.ask_question.requirement_question.positive.prompt_instruction`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive`
- `message_templates.requirement_collection.contextual_suggestions.marketing_project`
- `conversation.states.default`
- `intent_system.intents.ask_question.priority`
- `intent_system.intents.feedback`
- `llm.scenario_params.category_classifier.max_tokens`
- `message_templates.fallback.processing_failed`
- `message_reply_system.generators.capabilities_generator.fallback_template`
- `strategies.GLOBAL.reject.negative.priority`
- `llm.models.openrouter-gemini-flash.max_tokens`
- `intent_system.intents.general_chat.examples`
- `intent_system.intents.composite_knowledge_requirement.description`
- `knowledge_base.chroma_db.path`
- `message_templates.system.document.finalized`
- `strategies.GLOBAL.business_requirement`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral`
- `message_templates.requirement_collection.continue`
- `llm.scenario_params.document_generator`
- `intent_system.intents.ask_question.description`
- `message_templates.formatting.history.user_prefix`
- `strategies.COLLECTING_INFO.provide_information.anxious.action`
- `intent_system.intents.request_clarification.examples`
- `message_templates.error.modification`
- `system.decision_engine.cache_ttl`
- `database.queries.summaries`
- `message_templates.introduction`
- `message_reply_system.a_b_testing.test_groups.greeting.variant_a`
- `thresholds.business.focus_point_priority_threshold`
- `message_templates.system.document`
- `llm.scenario_params.greeting_generator`
- `database.queries.documents.get_by_conversation`
- `message_templates.logging.info.domain_category_saved`
- `intent_system.intents.process_answer.action`
- `strategies.GLOBAL.provide_information.confused.action`
- `intent_system.state_transitions.DOCUMENTING.unknown`
- `message_templates.system.action_executor.failed`
- `strategies.GLOBAL.reject.neutral.action`
- `intent_system.state_transitions.IDLE.feedback`
- `intent_system.intents.confirm.supported_states`
- `database.connection.path`
- `message_reply_system.categories.confirmation.description`
- `message_templates.logging.error.initial_question_failed`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious.priority`
- `message_templates.greeting.requirement_analyst`
- `intent_system.intents.confirm.description`
- `message_templates.business.focus_points.progress_awareness.three_quarters_complete`
- `strategies.GLOBAL.request_clarification.term_clarification`
- `message_templates.logging.error`
- `message_templates.formatting.status`
- `message_reply_system.fallback_enabled`
- `system.fallback_enabled`
- `message_reply_system.a_b_testing.test_groups.greeting.traffic_split`
- `strategies.GLOBAL.reset.neutral`
- `llm.models.deepseek-chat.top_p`
- `llm.models.qwen-turbo-latest.temperature`
- `intent_system.intents.general_chat.priority`
- `intent_system.state_transitions`
- `message_templates.requirement_collection.contextual_suggestions.software_development`
- `strategies.DOCUMENTING.modify.neutral.priority`
- `message_reply_system.generators.empathy_generator.description`
- `performance.concurrency`
- `thresholds.confidence.semantic_matching`
- `thresholds.quality.max_word_count`
- `intent_system.intents.feedback.supported_states`
- `message_templates.user_interaction`
- `message_reply_system.generators.default_generator.description`
- `strategies.COLLECTING_INFO.complete.neutral.priority`
- `strategies.GLOBAL.provide_information.positive.priority`
- `database.tables.conversations`
- `strategies.error_handling`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious.action`
- `llm.scenario_mapping.empathy_generator`
- `thresholds.security.max_login_attempts`
- `message_templates.user_interaction.redirect`
- `strategies.GLOBAL.greeting.positive.priority`
- `strategies.IDLE.business_requirement.neutral`
- `strategies.IDLE._state_config.use_simplified_logic`
- `message_reply_system.generators.default_generator.enabled`
- `strategies.DOCUMENTING._state_config.description`
- `message_templates.business.suggestions.smart_tips.technical_choices`
- `message_templates.system`
- `intent_system.intents.business_requirement.priority`
- `conversation.transitions.COLLECTING_INFO.confirm`
- `message_templates.capabilities`
- `performance.monitoring.metrics_interval`
- `conversation.keyword_acceleration.rules.confirm`
- `intent_system.intents.provide_information.examples`
- `keyword_rules.greeting`
- `strategies.GLOBAL.reject.neutral.priority`
- `thresholds.limits.default_max_items`
- `message_templates.confirmation.reset`
- `strategies.COLLECTING_INFO.provide_information.anxious.priority`
- `message_templates.business.suggestions.description_guidance`
- `intent_system.intents.domain_specific_query.supported_states`
- `strategies.COLLECTING_INFO.reject.neutral.prompt_instruction`
- `message_templates.logging.warning`
- `thresholds.business.document_quality_threshold`
- `database.queries.focus_points.clear_processing`
- `strategies.GLOBAL.provide_information.neutral`
- `message_reply_system.generators.introduction_generator.instruction`
- `llm.scenario_params.domain_guidance_generator.max_tokens`
- `strategies.GLOBAL.provide_information.positive.prompt_instruction`
- `security.input_validation.forbidden_patterns`
- `llm.scenario_params.default.provider`
- `intent_system.state_transitions.IDLE.unknown`
- `llm.scenario_mapping.category_classifier`
- `knowledge_base.logging.level`
- `message_reply_system.a_b_testing.enabled`
- `security.input_validation.enabled`
- `strategies.COLLECTING_INFO.provide_information.confused.action`
- `message_templates.prompts.restart`
- `message_templates.capabilities.main`
- `business_rules.retry`
- `intent_system.intents.feedback.priority`
- `knowledge_base.retrieval.max_context_length`
- `intent_system.decision_rules.confidence_thresholds.low`
- `strategies.COLLECTING_INFO.request_clarification.neutral.priority`
- `llm.models.doubao-1.5-Lite.model_name`
- `strategies.IDLE.ask_question.requirement_question.neutral`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral.action`
- `message_reply_system.categories.clarification`
- `strategies.GLOBAL.reject.negative`
- `message_templates.guidance.initial`
- `llm.models.qwen-plus.provider`
- `llm.scenario_params.greeting_generator.max_tokens`
- `conversation.keyword_acceleration`
- `message_templates.user_interaction.instructions.full_prompt_unknown`
- `conversation.transitions.COLLECTING_INFO.business_requirement`
- `security.data_protection.encrypt_sensitive`
- `conversation.transitions.CATEGORY_CLARIFICATION.restart`
- `llm.models.deepseek-chat.timeout`
- `llm.scenario_params.information_extractor.timeout`
- `strategies.COLLECTING_INFO.request_clarification.anxious.action`
- `llm.scenario_params.information_extractor`
- `database.queries.conversations.create_new`
- `llm.models.qwen-turbo-latest.timeout`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious.prompt_instruction`
- `strategies.COLLECTING_INFO.complete.positive.priority`
- `message_templates.user_interaction.processing.idle_modify_intent`
- `strategies.COLLECTING_INFO.provide_information.neutral.prompt_instruction`
- `strategies.GLOBAL.unknown.confused.action`
- `strategies.DOCUMENTING.restart.neutral.priority`
- `strategies.COLLECTING_INFO.request_clarification.confused.action`
- `message_reply_system.generators.clarification_generator.temperature`
- `message_reply_system.generators.introduction_generator.description`
- `strategies.COLLECTING_INFO.ask_question.neutral`
- `message_templates.business.suggestions.no_pending`
- `llm.models.qwen-plus.temperature`
- `intent_system.intents.system_capability_query.supported_states`
- `strategies.GLOBAL.reset`
- `strategies.IDLE.business_requirement.positive.prompt_instruction`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive.action`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive.prompt_instruction`
- `strategies.GLOBAL.provide_information.neutral.priority`
- `performance.monitoring.log_slow_queries`
- `strategies.GLOBAL.ask_question.anxious.prompt_instruction`
- `message_templates.system.session.reset_complete`
- `llm.scenario_params.intent_recognition`
- `conversation.keyword_acceleration.rules.ask_question`
- `message_templates.user_interaction.instructions`
- `intent_system.intents.restart.action`
- `intent_system.intents.restart`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral.prompt_instruction`
- `strategies.GLOBAL.business_requirement.marketing_requirement`
- `strategies.GLOBAL.ask_question.technical_question.confused.action`
- `message_reply_system.analytics.track_response_time`
- `thresholds.performance.timeout.long`
- `llm.default_model`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious.action`
- `intent_system.decision_rules.default_action`
- `llm.scenario_params.empathy_generator.timeout`
- `llm.scenario_params.domain_classifier.max_tokens`
- `message_templates.business.focus_points.progress_awareness`
- `llm.scenario_mapping.optimized_question_generation`
- `message_templates.fallback.unknown_situation`
- `strategies.GLOBAL.business_requirement.anxious.priority`
- `strategies.DOCUMENTING.reject.negative`
- `strategies.COLLECTING_INFO.process_answer.anxious`
- `message_reply_system.categories.greeting.priority`
- `strategies.COLLECTING_INFO.request_clarification.anxious.priority`
- `strategies.COLLECTING_INFO.provide_information.answer_question`
- `strategies.COLLECTING_INFO.provide_information.positive.prompt_instruction`
- `llm.scenario_params.information_extractor.temperature`
- `strategies.COLLECTING_INFO.reject.negative.prompt_instruction`
- `security.access_control.max_requests_per_minute`
- `message_templates.system.processing.special_state_logic`
- `strategies.IDLE.ask_question.requirement_question.neutral.priority`
- `message_templates.confirmation.document_finalized`
- `message_templates.business.focus_points.progress_awareness.quarter_complete`
- `database.queries.conversations.get_expired`
- `strategies.GLOBAL.provide_information.neutral.action`
- `conversation.keyword_acceleration.rules.business_requirement`
- `intent_system.intents.composite_knowledge_requirement.priority`
- `intent_system.intents.general_chat.description`
- `message_templates.greeting.friendly`
- `message_templates.business.suggestions.smart_tips.user_experience`
- `intent_system.state_transitions.IDLE.emotional_support`
- `strategies.COLLECTING_INFO.request_clarification.anxious`
- `message_templates.fallback.emergency`
- `message_templates.logging.info.state_transition_collecting`
- `strategies.GLOBAL.ask_question.technical_question.neutral.action`
- `strategies.GLOBAL.business_requirement.positive.prompt_instruction`
- `llm.scenario_params.empathy_generator.temperature`
- `system.description`
- `thresholds.performance.retry.llm_request`
- `thresholds.security.token_expiry`
- `strategies.COLLECTING_INFO.request_clarification.confused`
- `message_templates.business.focus_points.empty_list`
- `message_templates.prompts.domain_guidance.instruction`
- `llm.models.qwen-plus.max_retries`
- `message_reply_system.generators.greeting_generator.description`
- `message_templates.prompts.introduction.instruction`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive.action`
- `strategies.GLOBAL.request_clarification.neutral.prompt_instruction`
- `message_templates.logging.error.intent_processing_failed`
- `message_reply_system.a_b_testing`
- `thresholds.performance.timeout.database`
- `development.testing.use_test_db`
- `llm.models.qwen-intent.api_base`
- `knowledge_base.document_processing.chunk_size`
- `database.tables.documents.auto_backup`
- `performance.monitoring.slow_query_threshold`
- `conversation.keyword_acceleration.rules.general_chat`
- `conversation.keyword_acceleration.rules.greeting`
- `strategies.COLLECTING_INFO.complete.positive.prompt_instruction`
- `message_templates.logging.warning.dynamic_reply_not_initialized`
- `llm.scenario_mapping.greeting_generator`
- `integrations.knowledge_base`
- `knowledge_base.document_processing.supported_formats`
- `llm.scenario_params.apology_generator.max_tokens`
- `message_reply_system.generators.chat_generator.enabled`
- `strategies.COLLECTING_INFO.request_clarification.neutral.prompt_instruction`
- `message_reply_system.generators.introduction_generator.enabled`
- `strategies.COLLECTING_INFO.process_answer.anxious.prompt_instruction`
- `message_templates.system.document.confirmation_prefix`
- `strategies.GLOBAL.complete.positive.priority`
- `llm.models.qwen-max-latest`
- `llm.scenario_params.domain_guidance_generator`
- `intent_system.state_transitions.COLLECTING_INFO.request_clarification`
- `database.queries.conversations.delete_expired`
- `system.performance.cache_enabled`
- `strategies.GLOBAL.business_requirement.design_requirement`
- `message_reply_system.generators.introduction_generator.temperature`
- `message_templates.clarification.document_refinement`
- `message_templates.error.general_unknown`
- `message_templates.conversation.default.requirement_prompt`
- `message_templates.error.knowledge_base_not_found`
- `thresholds.performance.timeout.short`
- `development.debug.log_requests`
- `llm.scenario_params.greeting_generator.timeout`
- `strategies.GLOBAL.ask_question.confused.action`
- `message_reply_system.generators.capabilities_generator.max_tokens`
- `message_templates.error.processing`
- `message_templates.logging.info.focus_points_reset`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral`
- `llm.scenario_params.domain_classifier`
- `strategies.GLOBAL.provide_information.positive`
- `business_rules.action_handlers.handler_classes.CompositeHandler`
- `message_templates.prompts.domain_guidance`
- `conversation.transitions.CATEGORY_CLARIFICATION`
- `message_templates.user_interaction.defaults.unknown_intent`
- `message_templates.business.question`
- `database.tables.focus_points`
- `conversation.transitions.IDLE.ask_question`
- `conversation.keyword_acceleration.enabled`
- `intent_system.state_transitions.COLLECTING_INFO.provide_information`
- `llm.scenario_params.optimized_question_generation`
- `system.decision_engine.enable_caching`
- `strategies.GLOBAL.ask_question.technical_question.neutral.priority`
- `conversation.transitions.IDLE.greeting`
- `strategies.state`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious.action`
- `strategies.DOCUMENTING.modify`
- `strategies.GLOBAL.confirm.neutral.prompt_instruction`
- `message_reply_system.generators.clarification_generator.instruction`
- `conversation.keyword_acceleration.rules.emotional_support.keywords`
- `intent_system.intents.search_knowledge_base.action`
- `message_templates.system.state.db_update_success`
- `strategies.COLLECTING_INFO`
- `strategies.GLOBAL.request_clarification.anxious`
- `message_templates.exception.suggestions`
- `knowledge_base.performance.cache_enabled`
- `message_templates.requirement_collection.contextual_suggestions.design_project`
- `strategies.COLLECTING_INFO.complete`
- `strategies.GLOBAL.complete`
- `business_rules.retry.max_pending_attempts`
- `thresholds.business`
- `intent_system.state_transitions.DOCUMENTING.modify`
- `llm.scenario_params.empathy_generator`
- `intent_system.intents.process_query.supported_states`
- `llm.scenario_mapping.clarification_generator`
- `database.tables.documents`
- `database.queries.conversations.get_active`
- `strategies.GLOBAL.request_clarification.question_clarification`
- `intent_system.intents.provide_information`
- `message_reply_system.generators.clarification_generator.description`
- `intent_system.intents.composite_knowledge_requirement.examples`
- `message_templates.guidance.proactive_suggestions.welcome_with_examples`
- `conversation.transitions.DIRECT_SELECTION.selection_made`
- `llm.scenario_params.intent_recognition.max_tokens`
- `intent_system.state_transitions.IDLE.system_capability_query`
- `integrations.knowledge_base.enabled`
- `strategies.GLOBAL.request_clarification.anxious.priority`
- `strategies.DOCUMENTING.confirm.positive.prompt_instruction`
- `message_reply_system.generators.clarification_generator`
- `strategies.COLLECTING_INFO.provide_information.positive.action`
- `strategies.GLOBAL.ask_question.requirement_question`
- `strategies.GLOBAL.provide_information.confused`
- `strategies.DEFAULT_STRATEGY`
- `message_templates.system.processing.message_received`
- `message_templates.chat.general`
- `llm.scenario_mapping.domain_classifier`
- `conversation.transitions.DOMAIN_CLARIFICATION.restart`
- `message_reply_system.categories.error`
- `strategies.COLLECTING_INFO.reject.neutral.priority`
- `llm.scenario_params.default.timeout`
- `strategies.GLOBAL.provide_information.anxious`
- `strategies.DOCUMENTING.modify.neutral.action`
- `thresholds.quality.min_word_count`
- `message_templates.empathy`
- `message_templates.business.question.optimization_failed`
- `thresholds.business.template_match_threshold`
- `llm.scenario_params.apology_generator.temperature`
- `llm.models.qwen-intent.max_retries`
- `system.logging.max_file_size`
- `strategies.COLLECTING_INFO.provide_information`
- `security.input_validation`
- `conversation.keyword_acceleration.rules.ask_question.intent`
- `database.queries.focus_points.batch_insert`
- `intent_system.intents.unknown.description`
- `llm.models.qwen-turbo-latest.provider`
- `message_reply_system.a_b_testing.test_groups.greeting`
- `message_reply_system.generators.greeting_generator.max_tokens`
- `message_templates.conversation.modification`
- `strategies.GLOBAL.reject.neutral.prompt_instruction`
- `database.queries.focus_points.reset_all_status`
- `conversation.transitions.DOMAIN_CLARIFICATION.clarification_success`
- `llm.models.qwen-max-latest.top_p`
- `business_rules.action_handlers.handler_classes.ConversationHandler`
- `llm.models.qwen-turbo-latest`
- `message_templates.greeting.new_project`
- `message_templates.system.session.no_domain_info`
- `llm.scenario_mapping.apology_generator`
- `intent_system.state_transitions.IDLE`
- `message_templates.business.extraction.point_detail`
- `strategies.DOCUMENTING._state_config.use_simplified_logic`
- `database.queries.messages.save_message`
- `thresholds.quality`
- `strategies.GLOBAL.business_requirement.neutral.prompt_instruction`
- `llm.models.doubao-1.5-Lite.max_retries`
- `strategies.COLLECTING_INFO.process_answer.confused.action`
- `thresholds.security.rate_limit_per_minute`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious`
- `message_templates.guidance`
- `thresholds.limits.log_max_entries`
- `message_templates.requirement_collection.clarification`
- `strategies.COLLECTING_INFO._state_config.description`
- `strategies.DOCUMENTING._state_config.fallback_intent`
- `strategies.GLOBAL.complete.positive`
- `thresholds.quality.abuse_detection_threshold`
- `strategies.COLLECTING_INFO.confirm.neutral.priority`
- `intent_system.intents.feedback.description`
- `strategies.GLOBAL.business_requirement.positive.action`
- `llm.scenario_mapping.document_generator`
- `database.queries.messages.get_conversation_history_limited`
- `message_templates.confirmation`
- `message_reply_system.generators.chat_generator.agent_name`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral`
- `message_templates.logging.info.reset_status`
- `strategies.GLOBAL.request_clarification.neutral`
- `strategies.COLLECTING_INFO.request_clarification`
- `llm.models.deepseek-chat.max_tokens`
- `message_templates.guidance.proactive_suggestions`
- `strategies.GLOBAL.provide_information.anxious.priority`
- `system.performance.llm_timeout`
- `business_rules.requirement_collection.min_focus_points`
- `intent_system.state_transitions.DOCUMENTING`
- `business_rules.document_confirmation`
- `message_templates.error.request_processing`
- `intent_system.intents.emotional_support.supported_states`
- `llm.scenario_mapping`
- `intent_system.state_transitions.IDLE.domain_specific_query`
- `message_templates.error.document_modification`
- `strategies.GLOBAL.provide_information.confused.priority`
- `llm.models.openrouter-gemini-flash.temperature`
- `llm.scenario_params.default.max_retries`
- `knowledge_base.document_processing.chunk_overlap`
- `knowledge_base.features.rag_query`
- `message_reply_system.generators.greeting_generator.agent_name`
- `message_templates.logging.warning.llm_no_valid_unknown`
- `intent_system.state_transitions.IDLE.general_chat`
- `message_templates.logging.debug.no_active_state`
- `llm.models.doubao-pro-32k.timeout`
- `message_templates.conversation`
- `conversation.states`
- `thresholds.security.rate_limit_per_hour`
- `llm.models.qwen-turbo-latest.api_base`
- `strategies.GLOBAL.unknown.confused.priority`
- `message_templates.empathy.negative_general`
- `business_rules.action_handlers.handler_classes`
- `llm.models.qwen-max-latest.model_name`
- `message_templates.logging.info.unknown_point`
- `strategies.GLOBAL.business_requirement.neutral.priority`
- `conversation.keyword_acceleration.rules.general_chat.intent`
- `message_templates.logging.error.knowledge_base_not_initialized`
- `message_reply_system.generators.clarification_generator.max_tokens`
- `llm.models.deepseek-chat`
- `development.testing.mock_external_apis`
- `llm.models.qwen-intent.temperature`
- `conversation.transitions.DOCUMENTING`
- `strategies.GLOBAL.unknown.neutral.priority`
- `intent_system.intents.unknown.action`
- `database.queries.conversations.get_domain_category`
- `llm.scenario_params.llm_service.max_tokens`
- `llm.scenario_params.category_classifier.temperature`
- `message_templates.business.extraction`
- `strategies.GLOBAL.unknown.anxious.action`
- `message_templates.logging.error.init_collecting_state`
- `message_reply_system.generators.chat_generator.instruction`
- `thresholds.confidence.decision_engine`
- `llm.scenario_params.category_classifier`
- `message_templates.business.suggestions.general_guidance`
- `intent_system.intents.feedback.action`
- `strategies.GLOBAL.business_requirement.software_development.neutral.prompt_instruction`
- `strategies.DOCUMENTING.modify.neutral.prompt_instruction`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral.action`
- `knowledge_base.performance.max_concurrent_queries`
- `strategies.GLOBAL.greeting.neutral.priority`
- `strategies.GLOBAL.ask_question.confused.prompt_instruction`
- `conversation.keyword_acceleration.rules.confirm.keywords`
- `llm.scenario_params.category_classifier.timeout`
- `message_templates.logging.warning.domain_restore_failed`
- `message_templates.system.action_executor.success`
- `message_templates.help.simple`
- `message_templates.system.initialization`
- `strategies.GLOBAL.ask_question`
- `llm.models.qwen-intent`
- `strategies.IDLE.ask_question.requirement_question`
- `message_reply_system.analytics.track_user_satisfaction`
- `strategies.COLLECTING_INFO.provide_information.confused.prompt_instruction`
- `knowledge_base.performance`
- `llm.scenario_params.default_generator.timeout`
- `message_templates.system.document.generated`
- `message_templates.error.system`
- `database.queries.documents.get_list`
- `llm.scenario_params.default`
- `llm.scenario_params`
- `intent_system.intents.restart.supported_states`
- `strategies.GLOBAL.request_clarification`
- `strategies.GLOBAL.greeting.positive`
- `message_reply_system.generators`
- `strategies.GLOBAL.request_clarification.question_clarification.anxious.priority`
- `message_reply_system.generators.greeting_generator.instruction`
- `message_templates.capabilities.full`
- `message_templates.prompts.greeting`
- `thresholds.security.burst_limit`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral.priority`
- `strategies.DOCUMENTING._state_config.fallback_action`
- `message_templates.conversation.modification.need_more_info`
- `intent_system.intents.ask_question.examples`
- `business_rules.quality_control.max_input_length`
- `thresholds.confidence.intent_recognition`
- `strategies.GLOBAL.business_requirement.anxious.action`
- `strategies.COLLECTING_INFO.provide_information.anxious`
- `strategies.GLOBAL.ask_question.anxious.action`
- `strategies.COLLECTING_INFO.reject.neutral`
- `intent_system.intents.modify`
- `keyword_rules.business_requirement`
- `intent_system.intents.request_clarification.priority`
- `strategies.GLOBAL.confirm.neutral`
- `database.connection.check_same_thread`
- `strategies.COLLECTING_INFO.provide_information.neutral`
- `llm.models.qwen-max-latest.timeout`
- `message_reply_system.generators.default_generator.instruction`
- `llm.models.qwen-turbo-latest.top_p`
- `message_templates.business.focus_points.skip_no_processing`
- `database.queries.concern_point_coverage.get_processing_points`
- `conversation.transitions.CATEGORY_CLARIFICATION.clarification_success`
- `intent_system.decision_rules.confidence_thresholds`
- `intent_system.intents.domain_specific_query.action`
- `message_reply_system.categories.empathy.fallback_template`
- `conversation.keyword_acceleration.rules.greeting.keywords`
- `llm.models.qwen-plus`
- `database.queries`
- `database.queries.concern_point_coverage`
- `message_templates.business.suggestions.single_point`
- `strategies.GLOBAL.reset.neutral.priority`
- `database.queries.focus_points.get_single_status`
- `intent_system.intents.process_answer.priority`
- `intent_system.intents.composite_knowledge_requirement`
- `message_templates.clarification.need_more_info`
- `message_templates.capabilities.explanation`
- `strategies.GLOBAL.business_requirement.neutral`
- `strategies.GLOBAL.greeting.neutral.prompt_instruction`
- `message_templates.greeting.simple`
- `business_rules.quality_control.spam_detection_enabled`
- `thresholds.quality.completeness_threshold`
- `message_templates.system.processing.general_decision_engine`
- `llm.models.doubao-1.5-Lite.provider`
- `message_templates.system.action_executor`
- `message_reply_system.generators.chat_generator.fallback_template`
- `message_templates.formatting.status.unknown_focus_point`
- `message_reply_system.last_updated`
- `database.queries.concern_point_coverage.insert_coverage`
- `strategies.COLLECTING_INFO.complete.neutral.prompt_instruction`
- `strategies.IDLE.business_requirement.anxious.priority`
- `message_reply_system.generators.capabilities_generator.temperature`
- `strategies.COLLECTING_INFO.ask_question.neutral.prompt_instruction`
- `strategies.GLOBAL.ask_question.technical_question.confused.prompt_instruction`
- `llm.models.qwen-plus.max_tokens`
- `message_templates.logging.error.document_modification_failed`
- `intent_system.intents.modify.action`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious.priority`
- `thresholds.performance.retry.persistent_retry`
- `llm.scenario_params.llm_service.timeout`
- `strategies.DOCUMENTING.confirm.positive.action`
- `database.queries.conversations.get_info`
- `security.access_control.rate_limiting`
- `strategies.COLLECTING_INFO.provide_information.confused.priority`
- `message_reply_system.generators.empathy_generator.max_tokens`
- `conversation.transitions.DOCUMENTING.modify`
- `message_templates.system.processing`
- `strategies.GLOBAL.business_requirement.software_development.neutral`
- `llm.scenario_params.default_generator`
- `strategies.GLOBAL.ask_question.anxious.priority`
- `strategies.DOCUMENTING.general_request.neutral.prompt_instruction`
- `thresholds.performance.timeout`
- `intent_system.intents.greeting.priority`
- `knowledge_base.document_processing`
- `intent_system.intents.emotional_support.action`
- `message_templates.requirement_collection.contextual_suggestions`
- `strategies.COLLECTING_INFO.skip.neutral.action`
- `message_templates.greeting.basic`
- `strategies.GLOBAL.request_clarification.neutral.action`
- `message_reply_system.analytics`
- `development.debug.mock_llm`
- `message_reply_system.generators.capabilities_generator.enabled`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral.action`
- `intent_system.intents.process_query.description`
- `strategies.GLOBAL.provide_information.confused.prompt_instruction`
- `strategies.GLOBAL.unknown.anxious.prompt_instruction`
- `thresholds.business.response_time_threshold`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral.prompt_instruction`
- `message_templates.requirement_collection`
- `strategies.COLLECTING_INFO.reject.neutral.action`
- `strategies.GLOBAL.ask_question.requirement_question.neutral.priority`
- `thresholds.limits.min_focus_points`
- `intent_system.intents.domain_specific_query.description`
- `strategies.GLOBAL.ask_question.requirement_question.anxious`
- `strategies.COLLECTING_INFO.skip.neutral`
- `system.logging`
- `message_templates.user_interaction.processing.llm_success_unknown`
- `strategies.DOCUMENTING.restart`
- `performance.concurrency.queue_size`
- `conversation.keyword_acceleration.rules.general_chat.keywords`
- `integrations.knowledge_base.update_interval`
- `message_templates.business`
- `message_templates.exception.general_request.processing_error`
- `message_templates.business.suggestions.general_teaching_guidance`
- `thresholds.limits.max_concurrent`
- `business_rules.requirement_collection.max_focus_points`
- `llm.scenario_params.document_generator.max_tokens`
- `thresholds.confidence.default`
- `intent_system.intents.provide_information.priority`
- `message_reply_system.generators.capabilities_generator.instruction`
- `message_templates.prompts.restart.instruction`
- `message_templates.error.document_generation_failed`
- `llm.models.doubao-pro-32k`
- `intent_system.intents.confirm.action`
- `intent_system.intents.emotional_support.examples`
- `message_templates.logging.error.load_focus_points_failed`
- `message_reply_system.generators.chat_generator.max_tokens`
- `message_templates.system.document.project_name_template`
- `thresholds.limits.max_results`
- `message_templates.system.document.content_error`
- `message_templates.clarification.general_request`
- `thresholds.confidence.very_high`
- `strategies.COLLECTING_INFO.request_clarification.confused.prompt_instruction`
- `llm.scenario_params.intent_recognition.timeout`
- `message_templates.error.emergency_fallback`
- `llm.scenario_mapping.domain_guidance_generator`
- `database.queries.conversations`
- `keyword_rules.emotional_support`
- `keyword_rules.confirm`
- `strategies.DOCUMENTING.modify.neutral`
- `llm.scenario_params.optimized_question_generation.temperature`
- `llm.scenario_params.conversation_flow.max_tokens`
- `intent_system.intents.request_clarification.action`
- `conversation.transitions.DOMAIN_CLARIFICATION.clarification_failed`
- `message_templates.business.focus_points.skip_processing`
- `strategies.GLOBAL.request_clarification.term_clarification.confused.priority`
- `performance.monitoring.enabled`
- `message_reply_system.categories.guidance.fallback_template`
- `conversation.keyword_acceleration.rules.confirm.intent`
- `message_reply_system.generators.clarification_generator.agent_name`
- `strategies.GLOBAL.ask_question.neutral.action`
- `message_templates.exception.general_request`
- `database.queries.messages`
- `strategies.GLOBAL.ask_question.requirement_question.anxious.priority`
- `strategies.GLOBAL.ask_question.technical_question.neutral.prompt_instruction`
- `message_templates.user_interaction.defaults.requirement_prompt`
- `message_reply_system.categories.completion.enabled`
- `llm.models.openrouter-gemini-flash`
- `message_templates.introduction.youji_platform`
- `intent_system.decision_rules`
- `intent_system.intents.restart.description`
- `message_templates.conversation.restart`
- `strategies.GLOBAL.unknown.anxious.priority`
- `strategies.GLOBAL.business_requirement.marketing_requirement.anxious`
- `strategies.DOCUMENTING._state_config.priority_order`
- `llm.scenario_mapping.intent_recognition`
- `intent_system.state_transitions.COLLECTING_INFO`
- `llm.scenario_params.domain_classifier.temperature`
- `thresholds.performance.retry.quick_retry`
- `strategies.GLOBAL.ask_question.technical_question.confused`
- `strategies.IDLE.business_requirement.neutral.action`
- `performance.cache.ttl`
- `intent_system.intents.ask_question`
- `strategies.COLLECTING_INFO.process_answer.confused`
- `intent_system.intents.system_capability_query.priority`
- `intent_system.intents.system_capability_query.action`
- `integrations.external_apis`
- `strategies.COLLECTING_INFO.confirm.neutral.prompt_instruction`
- `security.access_control`
- `database.connection.timeout`
- `llm.models.doubao-1.5-Lite.api_base`
- `conversation.transitions.IDLE.business_requirement`
- `strategies.COLLECTING_INFO.skip.neutral.priority`
- `strategies.DOCUMENTING.general_request.neutral`
- `message_templates.logging.warning.conversation_history_failed`
- `message_templates.business.focus_points.progress_awareness.nearly_complete`
- `message_templates.chat.simple`
- `llm.models.qwen-max-latest.max_retries`
- `strategies.GLOBAL.complete.positive.action`
- `strategies.GLOBAL.business_requirement.anxious.prompt_instruction`
- `llm.models.qwen-plus.model_name`
- `message_templates.logging.error.format_focus_points_failed`
- `thresholds.quality.spam_detection_threshold`
- `strategies.GLOBAL.provide_information.anxious.action`
- `llm.models.openrouter-gemini-flash.model_name`
- `message_templates.greeting.professional`
- `business_rules.action_handlers.handler_classes.KnowledgeBaseHandler`
- `performance.cache.max_size`
- `message_templates.error.general_fallback`
- `strategies.DEFAULT_STRATEGY.action`
- `intent_system.intents.domain_specific_query`
- `message_templates.error`
- `thresholds.confidence.high`
- `llm.models.doubao-pro-32k.api_key`
- `message_templates.business.focus_points.progress_awareness.half_complete`
- `intent_system.intents.unknown.examples`
- `database.queries.focus_points`
- `database.queries.concern_point_coverage.update_status`
- `llm.models.qwen-intent.api_key`
- `database.queries.messages.get_first_user_message`
- `message_templates.user_interaction.defaults.no_history`
- `message_templates.user_interaction.processing.intent_string_not_json`
- `conversation.transitions.COLLECTING_INFO`
- `message_templates.prompts.introduction`
- `message_templates.system.document.generation_start`
- `strategies.GLOBAL.business_requirement.neutral.action`
- `conversation.keyword_acceleration.rules.business_requirement.intent`
- `strategies.IDLE.business_requirement.positive.action`
- `knowledge_base.retrieval.top_k`
- `llm.models.deepseek-chat.model_name`
- `database.queries.focus_point_definitions.get_by_focus_id`
- `strategies.GLOBAL.restart.neutral.action`
- `llm.models.qwen-max-latest.api_key`
- `intent_system.intents.unknown.supported_states`
- `thresholds.limits.max_focus_points`
- `conversation.transitions.IDLE`
- `message_reply_system.categories.completion.priority`
- `business_rules.action_handlers.handler_classes.RequirementHandler`
- `thresholds.quality.max_input_length`
- `message_reply_system.categories.empathy.description`
- `keyword_rules.general_chat`
- `llm.models.deepseek-chat.api_base`
- `message_templates.system.processing.intent_detected`
- `system.logging.backup_count`
- `llm.models.deepseek-chat.provider`
- `message_reply_system.generators.capabilities_generator.description`
- `strategies.GLOBAL.ask_question.requirement_question.neutral.action`
- `strategies.error_handling.graceful_degradation`
- `llm.scenario_params.empathy_generator.max_tokens`
- `thresholds.performance.retry.file_access`
- `intent_system.intents.business_requirement.supported_states`
- `knowledge_base.features.mode_switching`
- `strategies.DOCUMENTING.reject.negative.action`
- `system.decision_engine.fallback_to_simplified`
- `message_templates.fallback.general`
- `database.queries.focus_points.update_status`
- `llm.models.doubao-pro-32k.max_tokens`
- `llm.models.qwen-intent.timeout`
- `message_templates.prompts.empathy.default_instruction`
- `llm.models.qwen-plus.top_p`
- `database.tables.conversations.auto_cleanup`
- `knowledge_base.safety.max_query_length`
- `intent_system.intents.general_chat`
- `message_templates.prompts.empathy`
- `database.queries.summaries.upsert_summary`
- `llm.scenario_params.optimized_question_generation.timeout`
- `security.data_protection.mask_personal_info`
- `strategies.COLLECTING_INFO.provide_information.answer_question.neutral.prompt_instruction`
- `thresholds.performance.retry.default`
- `message_templates.system.welcome`
- `message_templates.system.document.content_retrieval`
- `strategies.DOCUMENTING.general_request.neutral.priority`
- `strategies.GLOBAL.business_requirement.positive`
- `business_rules.retry.backoff_factor`
- `database.queries.conversations.update_last_activity`
- `message_templates.system.processing.fallback_handling`
- `message_reply_system.categories.error.fallback_template`
- `message_templates.system.initialization.action_executor_success`
- `knowledge_base.logging`
- `intent_system.intents.business_requirement.action`
- `knowledge_base.chroma_db.collection_name`
- `strategies.GLOBAL.provide_information.neutral.prompt_instruction`
- `message_reply_system.generators.default_generator.max_tokens`
- `intent_system.intents.composite_knowledge_requirement.supported_states`
- `message_templates.logging`
- `strategies.GLOBAL.business_requirement.software_development.neutral.priority`
- `knowledge_base.retrieval.similarity_threshold`
- `llm.scenario_params.domain_guidance_generator.timeout`
- `strategies.GLOBAL.request_clarification.term_clarification.confused.action`
- `database.queries.concern_point_coverage.get_by_conversation`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive.prompt_instruction`
- `strategies.state.auto_save`
- `knowledge_base.role_filters.allowed_roles`
- `business_rules.action_handlers`
- `llm.scenario_mapping.llm_service`
- `strategies.COLLECTING_INFO.process_answer.anxious.priority`
- `strategies.IDLE._state_config.description`
- `intent_system.intents.modify.examples`
- `message_templates.business.suggestions.no_suggestions`
- `strategies.COLLECTING_INFO.request_clarification.confused.priority`
- `conversation.transitions.DOCUMENTING.restart`
- `llm.models`
- `database.queries.documents.get_content`
- `message_reply_system.categories.clarification.description`
- `strategies.COLLECTING_INFO.process_answer.neutral.action`
- `strategies.GLOBAL.reject`
- `intent_system.state_transitions.COLLECTING_INFO.process_answer`
- `intent_system.intents.search_knowledge_base.priority`
- `strategies.IDLE.ask_question.neutral.prompt_instruction`
- `intent_system.intents.restart.examples`
- `message_reply_system.categories.empathy.priority`
- `strategies.GLOBAL.ask_question.confused.priority`
- `conversation.transitions.IDLE.domain_classification_failed`
- `message_reply_system.generators.greeting_generator`
- `strategies.IDLE.greeting`
- `message_templates.system.session`
- `intent_system.state_transitions.COLLECTING_INFO.confirm`
- `llm.scenario_params.clarification_generator.timeout`
- `message_templates.logging.info.domain_transition_collecting`
- `database.tables`
- `strategies.DOCUMENTING.restart.neutral.action`
- `message_reply_system.categories`
- `strategies.COLLECTING_INFO.modify.neutral.prompt_instruction`
- `strategies.IDLE.ask_question.requirement_question.neutral.action`
- `intent_system.state_transitions.IDLE.ask_question`
- `llm.models.doubao-pro-32k.provider`
- `intent_system.intents.modify.priority`
- `strategies.reply.personalization_enabled`
- `message_reply_system.generators.chat_generator.description`
- `thresholds.quality.relevance_threshold`
- `strategies.GLOBAL.unknown.neutral`
- `conversation.keyword_acceleration.rules.greeting.intent`
- `database.queries.focus_point_definitions`
- `llm.scenario_params.domain_guidance_generator.temperature`
- `intent_system.decision_rules.confidence_thresholds.medium`
- `message_templates.logging.debug.domain_restore_attempt`
- `llm.scenario_params.greeting_generator.temperature`
- `message_reply_system.generators.empathy_generator.instruction`
- `message_templates.user_interaction.defaults.detailed_requirement`
- `strategies.COLLECTING_INFO.provide_information.neutral.priority`
- `strategies.IDLE.business_requirement.anxious.prompt_instruction`
- `message_templates.logging.info.extraction_result`
- `strategies.GLOBAL.business_requirement.marketing_requirement.positive`
- `strategies.GLOBAL.unknown.confused`
- `thresholds.confidence.minimum`
- `message_templates.help.full`
- `strategies.GLOBAL.business_requirement.marketing_requirement.neutral`
- `business_rules.quality_control`
- `llm.models.qwen-turbo-latest.max_retries`
- `strategies.GLOBAL.business_requirement.design_requirement.neutral.priority`
- `message_templates.confirmation.restart`
- `thresholds.performance.retry.max_attempts`
- `strategies.GLOBAL.greeting`
- `message_templates.error.general_request_processing`
- `message_templates.conversation.restart.confirmation`
- `system.supported_languages`
- `strategies.DOCUMENTING.restart.neutral.prompt_instruction`
- `intent_system.intents.unknown`
- `strategies.GLOBAL.ask_question.technical_question.confused.priority`
- `message_templates.user_interaction.processing`
- `message_templates.prompts.chat.instruction`
- `message_templates.conversation.default`
- `strategies.COLLECTING_INFO.complete.neutral.action`
- `message_templates.logging.debug.session_init_complete`
- `intent_system.intents.domain_specific_query.examples`
- `strategies.GLOBAL.ask_question.confused`
- `strategies.IDLE.business_requirement.anxious`
- `message_templates.prompts.greeting.instruction`
- `strategies.DOCUMENTING.confirm.positive.priority`
- `message_reply_system.language`
- `message_templates.guidance.specific_requirement_help`
- `thresholds.performance.timeout.api_request`
- `message_templates.business.focus_points.generation_failed`
- `intent_system.intents.emotional_support`
- `message_templates.business.question.optimization_context`
- `keyword_rules.restart`
- `message_templates.logging.info`
- `message_reply_system.categories.confirmation`
- `database.queries.messages.get_messages_by_focus`
- `message_templates.introduction.full`
- `strategies.GLOBAL.unknown.confused.prompt_instruction`
- `database.queries.focus_points.complex_update`
- `message_reply_system.categories.clarification.fallback_template`
- `strategies.GLOBAL.unknown.neutral.action`
- `message_templates.error.message_processing`
- `strategies.COLLECTING_INFO._state_config.use_simplified_logic`
- `conversation.keyword_acceleration.rules.ask_question.keywords`
- `strategies.COLLECTING_INFO.process_answer.confused.prompt_instruction`
- `knowledge_base.safety`
- `llm.models.deepseek-chat.max_retries`
- `llm.models.doubao-1.5-Lite.top_p`
- `llm.models.qwen-max-latest.api_base`
- `database.queries.batch_size`
- `business_rules.action_handlers.handler_classes.DocumentHandler`
- `intent_system.intents.process_answer.supported_states`
- `llm.models.qwen-plus.api_base`
- `intent_system.intents.composite_knowledge_requirement.action`
- `llm.scenario_params.llm_service.temperature`
- `database.queries.sessions.get_session`
- `strategies.GLOBAL.ask_question.requirement_question.anxious.action`
- `database.queries.max_results`
- `conversation.keyword_acceleration.rules.business_requirement.keywords`
- `strategies.COLLECTING_INFO.provide_information.answer_question.positive.priority`
- `keyword_rules`
- `message_reply_system.generators.introduction_generator.fallback_template`
- `database.queries.documents`
- `database.queries.documents.delete_document`
- `strategies.GLOBAL.complete.positive.prompt_instruction`
- `database.queries.backup.export_conversation`
- `message_templates.logging.debug`
- `message_templates.logging.warning.intent_config_not_found`
- `strategies.GLOBAL.restart`
- `strategies.GLOBAL.reject.negative.prompt_instruction`
- `strategies.DOCUMENTING.reject.negative.prompt_instruction`
- `strategies.GLOBAL.greeting.neutral.action`
- `development.testing`
- `strategies.GLOBAL.confirm`
- `strategies.GLOBAL.unknown.neutral.prompt_instruction`
- `performance.cache.enabled`
- `thresholds.confidence.domain_classification`
- `message_reply_system.supported_languages`
- `message_templates.requirement_collection.default_prompt`
- `strategies.COLLECTING_INFO.process_answer.neutral`
- `knowledge_base.document_processing.max_chunks_per_doc`
- `intent_system.intents.emotional_support.description`
- `strategies.COLLECTING_INFO.process_answer.neutral.priority`
- `message_reply_system.generators.default_generator.agent_name`
- `thresholds.security.sensitive_data_threshold`
- `database.queries.backup`
- `strategies.GLOBAL.restart.neutral`
- `strategies.GLOBAL.restart.neutral.priority`
- `message_templates.system.timeout`
- `llm.models.doubao-1.5-Lite.temperature`
- `message_templates.system.state`
- `message_reply_system.generators.empathy_generator.enabled`
- `strategies.COLLECTING_INFO.request_clarification.neutral.action`
- `message_templates.prompts.chat`
- `llm.models.doubao-pro-32k.top_p`
- `strategies.COLLECTING_INFO.provide_information.answer_question.anxious.prompt_instruction`
- `llm.scenario_params.apology_generator.timeout`
- `business_rules.action_handlers.handler_classes.GeneralRequestHandler`
- `strategies.GLOBAL.request_clarification.term_clarification.confused.prompt_instruction`
- `strategies.GLOBAL.ask_question.technical_question.neutral`
- `business_rules.document_confirmation.confirmation_keywords`
- `message_reply_system.a_b_testing.test_groups`
- `intent_system.intents.ask_question.action`
- `strategies.COLLECTING_INFO.modify`
- `message_templates.guidance.proactive_suggestions.completion_guidance`
- `message_reply_system.generators.capabilities_generator.agent_name`
- `strategies.GLOBAL.reject.neutral`
- `thresholds.performance.retry.database_operation`
- `intent_system.decision_rules.confidence_thresholds.high`
- `strategies.GLOBAL.confirm.neutral.action`
- `thresholds.confidence.keyword_matching`
- `message_reply_system.generators.greeting_generator.enabled`
- `strategies.GLOBAL.request_clarification.term_clarification.neutral.priority`
- `strategies.DOCUMENTING.confirm.neutral.action`
- `intent_system.intents.business_requirement`
- `intent_system.intents`
- `intent_system.intents.search_knowledge_base.supported_states`
- `llm.models.qwen-plus.api_key`
- `strategies.GLOBAL.ask_question.requirement_question.anxious.prompt_instruction`
- `llm.models.openrouter-gemini-flash.api_key`
- `strategies.COLLECTING_INFO.process_answer.anxious.action`
- `intent_system.intents.business_requirement.examples`
- `message_templates.logging.error.focus_points_not_found`
- `thresholds.limits.max_keywords`
- `message_templates.prompts`
- `message_templates.logging.error.determine_state`
- `intent_system.intents.greeting.examples`
- `thresholds.confidence`
- `message_templates.formatting.history.ai_prefix`
- `thresholds.limits.cache_max_size`
- `intent_system.intents.greeting.action`
- `message_templates.greeting.welcome_service`
- `message_reply_system.generators.empathy_generator`
- `message_templates.system.session.clear_messages_success`
- `thresholds.performance.retry.api_call`
- `intent_system.intents.restart.priority`
- `strategies.IDLE.ask_question.requirement_question.positive.action`
- `message_reply_system.enable_analytics`
- `business_rules.quality_control.min_input_length`
- `strategies.GLOBAL.ask_question.requirement_question.neutral`
- `database.queries.documents.check_exists`
- `knowledge_base.features.document_ingestion`
- `strategies.GLOBAL.ask_question.neutral.priority`
- `performance.cache`
- `intent_system.intents.business_requirement.description`
- `message_reply_system.generators.empathy_generator.fallback_template`
- `message_templates.exception.rephrase.detailed`
- `knowledge_base.features.intent_enhancement`
- `strategies.COLLECTING_INFO.process_answer.neutral.prompt_instruction`
- `llm.models.doubao-1.5-Lite.timeout`
- `message_templates.business.focus_points.all_completed`
- `message_templates.logging.info.domain_transition_documenting`
- `strategies.IDLE.business_requirement.neutral.prompt_instruction`
- `thresholds.quality.similarity_threshold`
- `system.language`
- `keyword_rules.ask_question`
- `knowledge_base.role_filters.enabled`
- `message_templates.business.suggestions.multiple_points`
- `message_reply_system.categories.clarification.priority`
- `intent_system.intents.greeting`
- `intent_system.intents.domain_specific_query.priority`
- `knowledge_base.enabled`
- `strategies.GLOBAL.provide_information.anxious.prompt_instruction`
- `message_reply_system.generators.default_generator`
- `intent_system.intents.confirm.priority`
- `message_reply_system.categories.empathy.enabled`
- `strategies.IDLE.greeting.neutral.action`
- `thresholds.performance.retry`
- `message_reply_system.generators.default_generator.fallback_template`
- `intent_system.intents.system_capability_query.examples`
- `message_templates.introduction.simple`
- `message_templates.logging.warning.unrecognized_subtype`
- `strategies.COLLECTING_INFO.confirm.neutral.action`
- `message_templates.logging.info.state_transition_documenting`
- `strategies.GLOBAL.ask_question.neutral.prompt_instruction`
- `strategies.COLLECTING_INFO.request_clarification.anxious.prompt_instruction`
- `strategies.GLOBAL.request_clarification.question_clarification.neutral.priority`
- `strategies.reply`
- `message_templates.fallback`
- `strategies.IDLE.ask_question.requirement_question.positive.priority`
- `intent_system.intents.process_query.examples`
- `llm.models.qwen-intent.top_p`
- `message_templates.system.session.restore_success`
- `message_reply_system.generators.empathy_generator.agent_name`
- `intent_system.intents.provide_information.supported_states`
