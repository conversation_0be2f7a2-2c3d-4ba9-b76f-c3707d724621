import React, { useState } from 'react';
import { Layout, Menu, theme, Typography, Space, Badge } from 'antd';
import {
  DashboardOutlined,
  SettingOutlined,
  BranchesOutlined,
  FileTextOutlined,
  ApiOutlined,
  MessageOutlined,
  DatabaseOutlined,
  ControlOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '概览',
    },
    {
      key: '/config',
      icon: <SettingOutlined />,
      label: 'LLM配置管理',
    },
    {
      key: '/scenario',
      icon: <BranchesOutlined />,
      label: '场景映射管理',
    },
    {
      key: '/business-rules',
      icon: <ControlOutlined />,
      label: '业务规则管理',
    },
    {
      key: '/database',
      icon: <DatabaseOutlined />,
      label: '数据库维护',
    },
    {
      key: '/template',
      icon: <FileTextOutlined />,
      label: '模板管理',
      children: [
        {
          key: '/template/prompts',
          icon: <ApiOutlined />,
          label: '提示词模板',
        },
        {
          key: '/template/messages',
          icon: <MessageOutlined />,
          label: '消息模板',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: colorBgContainer,
        }}
      >
        <div style={{ 
          height: 64, 
          margin: 16, 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start'
        }}>
          {!collapsed && (
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              后台管理
            </Title>
          )}
          {collapsed && (
            <SettingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          )}
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout>
        <Header 
          style={{ 
            padding: '0 24px', 
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0'
          }}
        >
          <Space>
            <Title level={3} style={{ margin: 0 }}>
              智能需求采集系统 - 后台管理
            </Title>
          </Space>
          
          <Space>
            <Badge status="processing" text="系统运行中" />
          </Space>
        </Header>
        
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;